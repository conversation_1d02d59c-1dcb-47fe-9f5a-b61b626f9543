{"cells": [{"cell_type": "markdown", "id": "40faeff4-33e9-4df8-aa9a-f055d41cf236", "metadata": {}, "source": ["# Materials Project에서 구조 가져오기 및 cubic supercell 만들기"]}, {"cell_type": "code", "execution_count": 1, "id": "21770f72-39bf-4c6f-9392-3b66836fa197", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Full Formula (Li12 P4 S16)\n", "Reduced Formula: Li3PS4\n", "abc   :   6.171766   8.018599  13.032818\n", "angles:  90.000000  90.000000  90.000000\n", "pbc   :       True       True       True\n", "Sites (32)\n", "  #  SP           a         b         c\n", "---  ----  --------  --------  --------\n", "  0  Li    0.389402  0.033497  0.667454\n", "  1  Li    0.610598  0.966503  0.332546\n", "  2  Li    0.889402  0.966503  0.832546\n", "  3  Li    0.110598  0.033497  0.167454\n", "  4  Li    0.610598  0.533497  0.332546\n", "  5  Li    0.389402  0.466503  0.667454\n", "  6  Li    0.110598  0.466503  0.167454\n", "  7  Li    0.889402  0.533497  0.832546\n", "  8  Li    0.5       0         0\n", "  9  Li    0         0         0.5\n", " 10  Li    0.5       0.5       0\n", " 11  Li    0         0.5       0.5\n", " 12  P     0.152389  0.25      0.910532\n", " 13  P     0.847611  0.75      0.089468\n", " 14  P     0.652389  0.75      0.589468\n", " 15  P     0.347611  0.25      0.410532\n", " 16  S     0.824676  0.25      0.89981\n", " 17  S     0.175324  0.75      0.10019\n", " 18  S     0.324676  0.75      0.60019\n", " 19  S     0.675324  0.25      0.39981\n", " 20  S     0.272423  0.25      0.059332\n", " 21  S     0.727577  0.75      0.940668\n", " 22  S     0.772423  0.75      0.440668\n", " 23  S     0.227577  0.25      0.559332\n", " 24  S     0.270965  0.036524  0.846139\n", " 25  S     0.729035  0.963476  0.153861\n", " 26  S     0.770965  0.963476  0.653861\n", " 27  S     0.229035  0.036524  0.346139\n", " 28  S     0.729035  0.536524  0.153861\n", " 29  S     0.270965  0.463476  0.846139\n", " 30  S     0.229035  0.463476  0.346139\n", " 31  S     0.770965  0.536524  0.653861\n"]}], "source": ["from pymatgen.ext.matproj import MPRester  \n", "\n", "API_KEY = \"NlY7ehbJQwKy9ZTmXlrpz46SMY6HZOpO\"\n", "\n", "with MPRester(API_KEY) as mpr:\n", "\n", "    structure = mpr.get_structure_by_material_id('mp-985583', conventional_unit_cell=True)\n", "\n", "    print(structure)"]}, {"cell_type": "code", "execution_count": 2, "id": "5296a42c-7a03-46b1-9478-6b8cb53a40d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 생성된 supercell의 격자 길이:\n", "12.343532 0.000000 0.000000\n", "-0.000000 16.037197 0.000000\n", "0.000000 0.000000 13.032818\n"]}], "source": ["from pymatgen.transformations.advanced_transformations import CubicSupercellTransformation\n", "\n", "# 1. 원하는 최소 길이 (단위: Å)\n", "min_length = 9.0\n", "\n", "# 2. <PERSON><PERSON><PERSON> supercell로 변환\n", "transformer = CubicSupercellTransformation(min_length=min_length)\n", "cubic_structure = transformer.apply_transformation(structure)\n", "\n", "# 3. 결과 확인\n", "print(\"✅ 생성된 supercell의 격자 길이:\")\n", "print(cubic_structure.lattice)"]}, {"cell_type": "code", "execution_count": 3, "id": "e268f5e6-aea5-4f64-8fc9-6c2fb0225a25", "metadata": {}, "outputs": [{"data": {"text/plain": ["128"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len(cubic_structure)"]}, {"cell_type": "markdown", "id": "77a33a94-15c7-45a6-8c98-0b1977ea9117", "metadata": {}, "source": ["# AIMD 인풋 만들기"]}, {"cell_type": "code", "execution_count": 4, "id": "032736d9-ea90-4673-ac05-ad5545eb70bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ MD 입력 생성 완료: 총 1500 steps @ 2fs (총 3 ps)\n"]}], "source": ["from pymatgen.io.vasp.sets import MPMDSet\n", "\n", "# 타임스텝 및 총 스텝 수 설정\n", "time_step_fs = 2  # fs\n", "total_time_ps = 3  # ps\n", "nsteps = int((total_time_ps * 1000) / time_step_fs)  # 15000 steps\n", "\n", "# MPMDSet 입력 설정\n", "md_set = MPMDSet(\n", "    structure=cubic_structure,\n", "    time_step=time_step_fs,\n", "    nsteps=nsteps,\n", "    start_temp=300,\n", "    end_temp=1200\n", ")\n", "\n", "# 입력 파일 저장\n", "md_set.write_input(\"MD_300K_to_1200K\")\n", "print(f\"✅ MD 입력 생성 완료: 총 {nsteps} steps @ 2fs (총 {total_time_ps} ps)\")"]}, {"cell_type": "code", "execution_count": 5, "id": "512dcc93-9439-45c1-a90c-d1d77e999b83", "metadata": {}, "outputs": [], "source": ["# # 타임스텝 및 총 스텝 수 설정\n", "# time_step_fs = 2  # fs\n", "# total_time_ps = 20  # ps\n", "# nsteps = int((total_time_ps * 1000) / time_step_fs)  # 15000 steps\n", "\n", "# # MPMDSet 입력 설정\n", "# md_set = MPMDSet(\n", "#     structure=second_structure,\n", "#     time_step=time_step_fs,\n", "#     nsteps=nsteps,\n", "#     start_temp=1200,\n", "#     end_temp=1200\n", "# )\n", "\n", "# # 입력 파일 저장\n", "# md_set.write_input(\"MD_1200K_to_1200K\")\n", "# print(f\"✅ MD 입력 생성 완료: 총 {nsteps} steps @ 2fs (총 {total_time_ps} ps)\")"]}, {"cell_type": "code", "execution_count": 6, "id": "8749f0b3-a0ff-4946-8c25-d25cc59723f5", "metadata": {}, "outputs": [], "source": ["# # 타임스텝 및 총 스텝 수 설정\n", "# time_step_fs = 2  # fs\n", "# total_time_ps = 3  # ps\n", "# nsteps = int((total_time_ps * 1000) / time_step_fs)  # 15000 steps\n", "\n", "# # MPMDSet 입력 설정\n", "# md_set = MPMDSet(\n", "#     structure=thrid_structure,\n", "#     time_step=time_step_fs,\n", "#     nsteps=nsteps,\n", "#     start_temp=1200,\n", "#     end_temp=300\n", "# )\n", "\n", "# # 입력 파일 저장\n", "# md_set.write_input(\"MD_1200K_to_300K\")\n", "# print(f\"✅ MD 입력 생성 완료: 총 {nsteps} steps @ 2fs (총 {total_time_ps} ps)\")"]}, {"cell_type": "markdown", "id": "083dbe52-550d-4d70-8d4c-b55db5aca744", "metadata": {}, "source": ["# CHGNET을 이용한 melt-quenching"]}, {"cell_type": "code", "execution_count": 27, "id": "eb510185-5173-44d0-965f-3fe649dc0608", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on cuda\n"]}], "source": ["# from chgnet.model.model import CHGNet\n", "# from chgnet.model.dynamics import MolecularDynamics\n", "# import warnings\n", "# warnings.filterwarnings(\"ignore\", module=\"pymatgen\")\n", "# warnings.filterwarnings(\"ignore\", module=\"ase\")\n", "\n", "# chgnet = CHGNet.load()\n", "\n", "# md = MolecularDynamics(\n", "#     atoms=cubic_structure,\n", "#     model=chgnet,\n", "#     ensemble=\"nvt\",\n", "#     temperature=1200,  # in K\n", "#     starting_temperature=300,\n", "#     timestep=2,  # in femto-seconds\n", "#     trajectory=\"md_out.traj\",\n", "#     logfile=\"md_out.log\",\n", "#     loginterval=100,\n", "# )\n", "# md.run(1500)  # run a 3 ps MD simulation"]}, {"cell_type": "code", "execution_count": 8, "id": "49974bc5-2a1e-4a12-8f85-8baecd2e601b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Full Formula (Li48 P16 S64)\n", "Reduced Formula: Li3PS4\n", "abc   :  12.343532  16.037197  13.032818\n", "angles:  90.000000  90.000000  90.000000\n", "pbc   :       True       True       True\n", "Sites (128)\n", "  #  SP            a          b          c    final_magmom  momenta\n", "---  ----  ---------  ---------  ---------  --------------  -------------------------------------------------------------------\n", "  0  Li     1.16871   -0.08953    1.17535      0.00377947   [-0.7937086919834612, -0.8387680559161861, 0.3798501897115017]\n", "  1  Li    -0.10634    0.848434   0.897296     0.00529665   [-0.14196033201761354, -0.8272158543969526, 1.294128944559604]\n", "  2  Li     2.31828   -0.914946  -0.326693     0.00785697   [-0.0682714668792746, 0.05604949533811955, 0.3950698310915712]\n", "  3  Li     1.10938    0.963676  -0.177335     0.0136437    [0.5172311836578296, -0.897391461068614, -0.27250826266537176]\n", "  4  Li     1.10607    1.0957     0.013093     0.0011856    [1.5228503689917428, 1.6274728122572406, 1.9628417840613486]\n", "  5  Li     1.09158    1.85988   -0.484962     0.00225353   [-0.2602085434420141, 0.902803394734396, 0.7328542226294351]\n", "  6  Li     0.164481   0.470278  -0.174545     0.000246644  [-1.097997078318318, -0.5605608108917118, 0.9399707777073634]\n", "  7  Li     1.71446    0.834782  -0.501935     0.00714755   [0.42547232252755524, 0.12241704658077854, 0.005968530805961374]\n", "  8  Li     1.81529    0.396502   0.767471     0.000928581  [-0.5317510774857039, 1.0224914332096715, -0.0744239504280659]\n", "  9  Li     1.37602    2.17661    0.007834     0.00488615   [-0.9612598938062134, 0.6603585680382579, 0.3814167212698957]\n", " 10  Li    -0.187012   0.076777   0.98043      0.00805473   [0.3217240258241592, 1.815130177043877, 0.39295078408185696]\n", " 11  Li     0.96545    1.48252    0.812942     0.00346774   [0.8014076172173974, 0.01718972019664972, -2.5065302874619038]\n", " 12  Li     1.24766   -0.785317   0.32584      0.00219929   [0.4569286268229761, 0.9337787428995958, -0.304831634102783]\n", " 13  Li     0.018659   1.338     -0.478908     0.0196002    [1.8580025033169478, -0.6546402554838078, 0.45893384689010613]\n", " 14  Li    -0.114262   0.186615   1.39886      0.00255489   [0.09050007771949899, -0.3885668560557557, -1.3499319489444193]\n", " 15  Li     0.561413   1.72772    0.593068     0.0149541    [-0.33547492508188353, 0.8806125304479617, 0.4437165435721057]\n", " 16  Li    -0.632227   0.338979  -0.084152     0.00355452   [0.947272360815547, 1.5339316189089123, -1.3894450857967169]\n", " 17  Li     1.89975    0.473763   1.26128      0.00361466   [0.8927705213865985, -0.6287841433977968, -0.13840256984454588]\n", " 18  Li     1.11482    0.368335   0.193182     0.0123599    [-0.11036445770706764, 0.5568092531332235, -0.7652753609459764]\n", " 19  Li     1.59255    1.30828    0.65478      0.0129115    [0.28140528843977103, 0.5121300074017097, 0.1435488960872556]\n", " 20  Li     0.393047  -0.22446    0.319035     0.00166631   [-1.5469553449803595, 1.0474720934858102, -1.1549938577583965]\n", " 21  Li     0.902765   0.237973   0.678572     0.00418776   [1.5558053727449466, 3.0799739965205775, 0.2194311037112872]\n", " 22  Li     0.754701  -0.358078  -0.028045     0.0061239    [-1.04070519038213, 0.791926091328509, -1.4363312650158129]\n", " 23  Li     1.03496    0.611507   0.670931     0.00902319   [0.7117149674247774, 0.04929350102404122, 0.0637359384958456]\n", " 24  Li     0.611809   0.23097    0.320394     0.0219381    [-0.10252985278891523, -0.07686747466138978, -0.018082539186157716]\n", " 25  Li     0.412714   0.507694   0.297167     0.00959086   [0.3443648534265433, 0.28399457389637367, -0.7662959578837122]\n", " 26  Li     1.44843    0.950661  -0.487196     0.00113958   [-0.4739928715797682, 0.5582354607621138, 0.983981994046236]\n", " 27  Li     0.353027   1.09438    0.311041     0.0101993    [0.0958778537026496, -0.8968690093262852, -0.8930343224511678]\n", " 28  Li     0.517013   1.08124   -0.008537     0.00832105   [0.21088410161162596, 1.0899634125134572, -0.08807758956305478]\n", " 29  Li     0.242564   0.908764  -0.366443     0.00347668   [0.9518378270018715, -1.3485860292437915, -0.26424252685934485]\n", " 30  Li     1.91429   -0.031464   0.588626     0.00199729   [-1.1009748158205268, -1.1691897244894982, -1.2978885891825076]\n", " 31  Li     1.54526    0.519952   0.454666     0.0142128    [-1.2811058029709144, 0.37198861575816167, 0.41840569837246316]\n", " 32  Li    -0.136656   0.280116  -0.078733     0.00337207   [-0.39084277047408955, -0.04857478827201048, 1.083086604929539]\n", " 33  Li     1.67429   -0.039725   0.245964     0.000822008  [-1.7099824742157992, -0.13707593946350555, 0.8308689031152966]\n", " 34  Li    -0.250799  -0.394062  -0.298362     0.0063602    [-0.8882397438814402, -1.8989262229312558, -2.435165859556468]\n", " 35  Li     0.328286   0.616453   0.13921      0.00564158   [0.025045324715510714, 0.103461061719882, 0.7867301454367374]\n", " 36  Li    -0.082711  -0.201276   1.36991      0.00582451   [-2.6196321074697284, -0.4855104210085395, 0.49807511353365863]\n", " 37  Li    -0.459344   0.778544   1.015        0.0172608    [-0.07552941278203137, 0.3288465053824594, 1.1384569823020896]\n", " 38  Li     0.023217  -0.341264   1.09642      0.00225228   [-1.2749470574642838, 0.6690266695187628, 1.1297233880699122]\n", " 39  Li     0.63801    0.313729   1.03757      0.0112266    [0.047522196443302765, -0.4886233588767347, 0.5363183219677182]\n", " 40  Li     1.66599    1.53285    0.085207     0.00244254   [-0.8660884105051312, 0.09648438149707433, 0.9217499831305921]\n", " 41  Li    -0.29426    0.869735   0.127506     0.00633895   [-0.6937104131663931, 0.8642149653568765, 0.6620634543270824]\n", " 42  Li     0.623956  -1.1082    -0.201295     0.0115446    [0.004926762033173812, -0.7375307570107317, -0.6870598718027737]\n", " 43  Li     0.940213   0.996958   0.241052     0.00321496   [-0.716602045798894, 1.8665213317654585, 0.22551839073094623]\n", " 44  Li     0.769044   0.607129  -0.705538     0.00129378   [0.032220796559556356, 0.6775267278033359, -1.7765073513686755]\n", " 45  Li     0.271708   0.359633   0.608341     0.0145777    [-0.17257601561129043, -0.06779868296978968, 0.45942021251440607]\n", " 46  Li     0.057966  -0.474852   0.475059     0.0122154    [0.5524581280525076, -0.09328554105468492, -0.023717943024048893]\n", " 47  Li    -0.280821   2.15755    0.610292     0.00810993   [0.6899881481292158, 0.022858909531879502, -0.22391908930239834]\n", " 48  P      0.083052   0.228585   0.852347     0.00090456   [1.2204231385531659, 3.102930882177075, -0.3698644422435769]\n", " 49  P     -0.077709   0.516283   1.0255       0.00498474   [-1.3328126228455273, -1.7386961899294764, -0.23217400523646034]\n", " 50  P      0.630776   0.170735   0.87545      0.00805354   [-0.4681493357355837, 4.095180184342318, 0.059873851031700676]\n", " 51  P      0.444988   0.514886   0.922903     0.00289857   [-1.0392353306024593, -0.2873787467923148, 0.6386186325440545]\n", " 52  P      0.021803   0.222555   0.23851      0.00454247   [2.8426070168230253, -0.6323498626536407, 1.1599058251684717]\n", " 53  P      0.421865   0.958092   0.116533     0.00259358   [0.7982395953155523, 0.5142641757500139, 2.0933816717289595]\n", " 54  P      0.450014   0.341937   0.212025     0.000152171  [1.1451227396392225, 1.1517633926404893, -1.2123238490007726]\n", " 55  P      0.916653   0.89712    0.082243     0.00842708   [-0.9512133967449088, -4.685312209657575, 0.49371275251789626]\n", " 56  P      0.282812   0.533348   0.672886     0.00211155   [-1.207983082834875, -3.713760563547878, -3.997586275645737]\n", " 57  P      0.326956   0.818314   0.745934     0.00185275   [1.944976465300931, -2.392143236267922, 0.02269438858762926]\n", " 58  P      0.782568   0.460633   0.531811     0.00135624   [-2.4659144224896563, 2.693679286282285, 2.166178358927482]\n", " 59  P      0.932757   0.807832   0.685129     0.00355142   [-2.2827845672515146, -0.6476233043669968, -2.640823124653178]\n", " 60  P      0.237915   0.025014   0.433229     0.0102867    [-0.23461860520934769, -1.78107045293168, -0.8825486717643348]\n", " 61  P      0.167085   0.652342   0.318068     0.00163609   [1.6614090688775482, -2.011710791069631, -2.30666900718259]\n", " 62  P      0.77172    0.048716   0.469856     0.00098598   [0.5548331193552851, -1.1680085168763399, -1.400074696140736]\n", " 63  P      0.599262   0.734707   0.353853     0.00543052   [0.16053916116377565, 1.4840741798372232, 1.572737127342856]\n", " 64  S      0.47155    0.156636   0.815981     0.026528     [-2.9405209928039677, -0.9591284969968294, 0.921059381114221]\n", " 65  S      0.323219   0.45569    1.00066      0.000305802  [-3.825336873514148, 1.3889911507285724, 0.38692575518235534]\n", " 66  S      1.11261    0.120431   0.75853      0.016674     [0.7234547063422132, -2.103337947273412, -0.7502462561552401]\n", " 67  S      0.953888   0.403715   1.0959       0.000437677  [-2.1583603445252684, -0.2654604297128363, -0.2940435898193018]\n", " 68  S     -0.501585   0.233748   0.120219     0.00965393   [-1.00378154198448, -2.4293029310589875, 3.3785475523030284]\n", " 69  S      0.414416   1.26891   -0.240803     0.0207568    [-1.2331420648144602, 2.3407638317883683, -1.7572944822181777]\n", " 70  S      0.429779   0.393574   0.470662     0.0302977    [1.0743808010652114, -2.008475251102177, 1.8791034691586617]\n", " 71  S      0.479769   0.9899     0.266164     0.00204974   [1.6591360487751956, -0.7412530095004043, 1.3324624872245705]\n", " 72  S      0.363852   0.428382   0.741457     0.0048614    [0.8425200054338312, 1.9060175686877459, 0.16075938264331951]\n", " 73  S      0.264581   0.754255   0.631481     0.00460702   [0.3245319778522401, 0.713362141884209, -1.4943684157797126]\n", " 74  S      0.893642   0.563762   0.536048     0.000624791  [1.1986579337143217, -0.4760516885910205, -3.172481735361229]\n", " 75  S      1.05337    0.89123    0.686442     0.0339307    [2.8945468405699186, 2.6642274940388506, 0.17433979598949004]\n", " 76  S     -0.147076  -0.222301   0.053918     0.00618862   [-2.871634406899543, -1.1369517707273553, 0.7435836991363645]\n", " 77  S      0.184194   0.714936   0.170158     0.00689989   [-2.1254498542573885, 0.584482359732377, -1.3591802090972638]\n", " 78  S      0.870705   0.120188   0.558984     0.0211709    [-0.8092137140322597, -2.0448027952749466, -2.3957007787706366]\n", " 79  S      0.724909   0.783406   0.269642     0.010999     [1.7072542790006784, -0.41320961342277357, -1.5863445316192313]\n", " 80  S      0.237403   0.234232  -0.09586      0.00682829   [-0.5475681371922738, -0.03401794499030032, 1.5220102764696803]\n", " 81  S     -0.145212   0.593279   0.130506     0.0249976    [-0.0424621543724356, 1.350617777080908, -0.26147366161799446]\n", " 82  S      0.734943   0.23871   -0.222201     0.0225576    [0.3849451764060904, -0.2996867919714254, 2.264176995743788]\n", " 83  S      0.51842    0.610347   0.021573     0.00140233   [0.871006620370544, -2.97674676621941, 1.4738359584920293]\n", " 84  S      0.170589   0.213887   1.16065      0.00710785   [1.8919687670992427, 0.14968640242381348, 2.975584156591156]\n", " 85  S      0.34152    0.850776   1.12069      0.00225452   [-1.2856404138492599, -1.0749930407238466, 1.519513491879948]\n", " 86  S      0.631084   0.362829   1.21192      0.00822459   [-0.42931192700423787, 0.9364067121748434, 2.2042176235013056]\n", " 87  S      0.803671   1.00022    1.11281      0.0241801    [1.6659193873447327, -0.22235251948884782, 0.06331577332260593]\n", " 88  S      0.197978   0.492389   0.542077     0.00903921   [1.5450575259802108, 0.32990286126961654, 2.3117784642240697]\n", " 89  S      0.419582   0.732402   0.795421     0.0234552    [3.3779281328725688, -0.5516316197627185, 1.9948435531331978]\n", " 90  S      0.700056   0.469856   0.663194     0.0137194    [-0.39167313723184094, -2.0903674514315256, -2.9810152574078748]\n", " 91  S      0.912243   0.754305   0.536825     0.00201397   [-3.157234167445116, -1.9492078448597128, -0.5863260845119814]\n", " 92  S      0.256928  -0.105479   0.415227     0.00648892   [1.493536139513296, 0.41990188180539917, -0.7977221551998303]\n", " 93  S      0.009959   0.642201   0.313678     0.00655669   [-1.8592051071067572, 1.0113316335897944, -2.209711667052619]\n", " 94  S      0.854954  -0.036127   0.395607     0.0182376    [1.4583017246845948, -0.31042636114030886, -3.8323307252677465]\n", " 95  S      0.516141   0.810144   0.47         0.0183292    [-0.7333517007118062, 1.432795606948568, -2.692577927907325]\n", " 96  S      0.012518   0.326755   0.773341     0.0249258    [2.017280115927987, 4.346998773824718, 0.2615471265978199]\n", " 97  S      0.053454   0.551677   0.956224     0.0138016    [-2.159306680706717, -0.1622263309476425, 1.1649028924189062]\n", " 98  S      0.662519   0.03723    0.836735     0.0252307    [-0.9456926239524521, 1.3551343609589672, 1.9127282293414332]\n", " 99  S      0.563473   0.436117   0.909103     0.00207925   [-1.059305900860476, -1.6400839490211612, 0.36225968717682705]\n", "100  S      0.0428     0.119576   0.367984     0.0179496    [-0.6698452014717159, -0.9734885106552296, -0.9653099824971828]\n", "101  S      0.559008   0.928244   0.040852     0.0165679    [-0.3880599729193862, -2.099321085174172, 0.4265836494105775]\n", "102  S      0.671253   0.160959   0.022594     0.0233905    [1.834522366542358, 0.7311537643603507, -1.4869158020583009]\n", "103  S      0.995529   0.848888   0.20964      0.00692637   [0.4581515689482796, -0.4342006062086159, 3.156653833911202]\n", "104  S      0.190793   0.599799   0.771233     0.00902604   [0.4341933633774003, 0.24691250944586945, -0.49087893675671096]\n", "105  S      0.414561   0.938111   0.679167     0.00919473   [-0.47772731104193067, 0.5799092951089015, -0.3822697998005825]\n", "106  S      0.729487   0.467444   0.375729     0.00310804   [3.187694837760983, -1.7592076680954771, -0.040076372903882135]\n", "107  S      0.949886   0.703824   0.787136     0.0101649    [-0.454501375430038, 0.019921200919397905, 0.4019009806813198]\n", "108  S      0.192272   0.047484   0.274768     0.021011     [-0.7408434284299814, 0.48672458771092164, -0.2577014023093301]\n", "109  S      0.240092   0.7064     0.439073     0.00749546   [-1.6178942402027807, 0.17771043809450784, -0.23143990053809196]\n", "110  S      0.732562   0.129855   0.366421     0.0143794    [0.9979918672975752, -2.5461730614594758, 1.4304725981993176]\n", "111  S      0.673597   0.66754    0.465605     0.00833878   [3.0837659998829383, -1.0138147403018167, -0.7949070886195271]\n", "112  S      0.114212   0.069355   0.520898     0.0131593    [2.7772325081088147, 0.6124227994857362, -1.451477595419517]\n", "113  S      0.325278   1.04484    0.014193     0.00134806   [-1.0268989345702852, 1.5989853209046447, -1.3107796209256264]\n", "114  S      0.348242   0.338253   0.339474     0.0200865    [0.061219134284262886, 1.911179320434123, -2.9233438742729034]\n", "115  S      1.06116    0.91931   -0.015804     0.00444315   [0.9655604923389152, -1.9863975698652583, -0.8374940950932873]\n", "116  S     -0.025007   0.187539   0.978506     0.00706886   [0.28964284105987, 0.7514105279440606, -2.2170444424412445]\n", "117  S     -0.19223    0.526584   0.904444     0.0138164    [-0.25865120874209446, 0.1956162553210481, 1.176219546442865]\n", "118  S      0.873499   0.212651   0.183177     0.0153682    [-0.9246063864723414, -0.657038979166765, 3.1514416947988706]\n", "119  S      0.585215   0.732893   0.750625     0.0267328    [2.0766977915402376, 1.9107687899435875, 2.0362106617728895]\n", "120  S      0.362665   0.094334   0.483334     0.0179555    [0.2964294321843357, 2.6242743638423716, -0.12974799429550085]\n", "121  S      0.242721   0.537137   0.281552     0.0026425    [0.9998663874589666, -0.5569317402914948, 0.7738445090345085]\n", "122  S      0.632117   0.026205   0.557985     0.0115693    [-1.7278482350343247, -1.2160216856486974, 2.5317036567707465]\n", "123  S      0.514585   0.636146   0.256931     0.00151527   [-3.1956423696690917, -0.5419225966890855, 0.8212264527439112]\n", "124  S      0.994535   0.325574   0.347444     0.00629529   [-0.4675786016017023, 1.047432971129691, 0.2547838377313211]\n", "125  S      0.231123   0.846661   0.855028     0.000488415  [0.3269005721762831, -2.494686013384798, 1.001593569763184]\n", "126  S      0.83125    0.340884   0.529744     0.0076617    [2.6776161489958565, -0.10986513604498108, -0.19227601904280203]\n", "127  S      0.774365   0.850024   0.713301     0.00136758   [1.502138200962103, 2.171705217378706, -1.074840390253305]\n"]}], "source": ["from ase.io.trajectory import Trajectory\n", "from pymatgen.io.ase import AseAtomsAdaptor\n", "\n", "traj = Trajectory(\"md_out.traj\")\n", "\n", "# get the non-charge-decorated structure\n", "second_structure = AseAtomsAdaptor.get_structure(traj[-1])\n", "print(second_structure)"]}, {"cell_type": "code", "execution_count": 9, "id": "a925feea-63de-4b6c-8c25-f8a8c6c4a1b9", "metadata": {}, "outputs": [], "source": ["# md2 = MolecularDynamics(\n", "#     atoms=second_structure,\n", "#     model=chgnet,\n", "#     ensemble=\"nvt\",\n", "#     temperature=1200,  # in K\n", "#     starting_temperature=1200,\n", "#     timestep=2,  # in femto-seconds\n", "#     trajectory=\"md_out2.traj\",\n", "#     logfile=\"md_out2.log\",\n", "#     loginterval=100,\n", "# )\n", "# md2.run(5000)  # run a 10 ps MD simulation"]}, {"cell_type": "code", "execution_count": 10, "id": "d230403b-09be-480c-a15c-79ece15f1f62", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"# generated using pymatgen\\ndata_Li3PS4\\n_symmetry_space_group_name_H-M   'P 1'\\n_cell_length_a   12.34353186\\n_cell_length_b   16.03719718\\n_cell_length_c   13.03281824\\n_cell_angle_alpha   90.00000000\\n_cell_angle_beta   90.00000000\\n_cell_angle_gamma   90.00000000\\n_symmetry_Int_Tables_number   1\\n_chemical_formula_structural   Li3PS4\\n_chemical_formula_sum   'Li48 P16 S64'\\n_cell_volume   2579.92006255\\n_cell_formula_units_Z   16\\nloop_\\n _symmetry_equiv_pos_site_id\\n _symmetry_equiv_pos_as_xyz\\n  1  'x, y, z'\\nloop_\\n _atom_site_type_symbol\\n _atom_site_label\\n _atom_site_symmetry_multiplicity\\n _atom_site_fract_x\\n _atom_site_fract_y\\n _atom_site_fract_z\\n _atom_site_occupancy\\n  Li  Li0  1  -0.37675116  -0.03984475  1.10046429  1\\n  Li  Li1  1  -0.15623615  0.90989289  0.30975123  1\\n  Li  Li2  1  1.41010679  0.04378186  0.32244736  1\\n  Li  Li3  1  0.61871873  0.20984100  -0.10835410  1\\n  Li  Li4  1  0.53490177  -0.26054267  0.51054564  1\\n  Li  Li5  1  -0.11642541  2.62852343  0.49781730  1\\n  Li  Li6  1  1.22159719  -0.20375503  -0.65230103  1\\n  Li  Li7  1  1.58830928  0.47232149  0.21493791  1\\n  Li  Li8  1  1.30552969  0.98782515  1.56905646  1\\n  Li  Li9  1  -0.11321219  1.12116931  0.88777358  1\\n  Li  Li10  1  1.19420492  0.80392078  1.14489517  1\\n  Li  Li11  1  0.92503077  1.32055877  0.93258995  1\\n  Li  Li12  1  -0.06520025  0.66143868  -0.20439626  1\\n  Li  Li13  1  -0.47647620  1.05891303  -0.44392094  1\\n  Li  Li14  1  0.42250497  -0.17455668  0.75846323  1\\n  Li  Li15  1  1.26714390  1.22704277  0.57580577  1\\n  Li  Li16  1  -0.26582011  -0.63000010  -0.10625332  1\\n  Li  Li17  1  -0.50109143  -0.11729154  1.00313088  1\\n  Li  Li18  1  0.91243303  -0.21584602  0.97248251  1\\n  Li  Li19  1  0.18718983  1.20948551  0.01779353  1\\n  Li  Li20  1  -0.33549197  -0.37096482  -0.33061823  1\\n  Li  Li21  1  0.66173620  0.85773459  0.35281599  1\\n  Li  Li22  1  0.59181651  -0.26054066  0.19569702  1\\n  Li  Li23  1  0.55959417  1.15491495  0.18170167  1\\n  Li  Li24  1  0.34497014  0.56355692  0.15082386  1\\n  Li  Li25  1  0.94599692  1.44010276  1.13171597  1\\n  Li  Li26  1  0.36853415  0.23871595  -0.56313232  1\\n  Li  Li27  1  1.07909992  1.02708645  0.00694252  1\\n  Li  Li28  1  1.56470457  0.47128912  0.79414379  1\\n  Li  Li29  1  0.35718700  0.62796837  0.93339323  1\\n  Li  Li30  1  1.84590615  0.61949221  1.27438209  1\\n  Li  Li31  1  0.18231420  1.46842642  0.72763841  1\\n  Li  Li32  1  1.11877053  -0.00931970  0.37275060  1\\n  Li  Li33  1  1.16540489  0.47224167  0.21884807  1\\n  Li  Li34  1  -1.42843222  0.84807074  -0.30558407  1\\n  Li  Li35  1  0.93342348  0.21860224  -0.43442691  1\\n  Li  Li36  1  -0.23248796  -0.06030317  0.64647164  1\\n  Li  Li37  1  -0.35209965  1.34574646  -0.30483079  1\\n  Li  Li38  1  0.07456542  0.29422512  0.74013322  1\\n  Li  Li39  1  1.17936601  -0.27041831  1.93165843  1\\n  Li  Li40  1  0.88261783  0.36067943  0.45918235  1\\n  Li  Li41  1  0.46363525  1.06859524  -1.18873294  1\\n  Li  Li42  1  1.67223596  0.32577937  0.36950631  1\\n  Li  Li43  1  -0.08962590  1.08667477  -0.32227187  1\\n  Li  Li44  1  0.28981326  0.39454983  -0.14671856  1\\n  Li  Li45  1  0.76636154  0.52900880  -0.07398509  1\\n  Li  Li46  1  -0.77353501  0.52380952  0.49552538  1\\n  Li  Li47  1  0.20241866  1.07331247  1.16415519  1\\n  P  P48  1  0.14821121  0.10729564  0.72874425  1\\n  P  P49  1  0.15474301  0.55665497  0.96925206  1\\n  P  P50  1  0.68401987  0.02706676  0.85240161  1\\n  P  P51  1  0.58776851  0.65557573  0.97216342  1\\n  P  P52  1  0.36333579  0.27302508  0.20399557  1\\n  P  P53  1  0.38972536  0.87115993  0.17836437  1\\n  P  P54  1  0.77205067  0.29260487  0.10518148  1\\n  P  P55  1  0.99721787  0.91612928  0.14404715  1\\n  P  P56  1  0.41916740  0.38391174  0.60082786  1\\n  P  P57  1  0.32080353  0.82562242  0.58334877  1\\n  P  P58  1  0.92879046  0.42837536  0.67019954  1\\n  P  P59  1  0.87163883  0.78559556  0.59706723  1\\n  P  P60  1  0.13128755  0.23005189  0.30069981  1\\n  P  P61  1  0.06184214  0.58101481  0.34685313  1\\n  P  P62  1  0.70136837  0.10866100  0.41806658  1\\n  P  P63  1  0.61490066  0.58859518  0.39962134  1\\n  S  S64  1  0.67324759  0.07713473  1.00717964  1\\n  S  S65  1  0.65292052  0.63317465  1.11574842  1\\n  S  S66  1  1.06647430  0.16928292  0.86509466  1\\n  S  S67  1  1.23297693  0.65595703  1.07831900  1\\n  S  S68  1  -0.23880783  0.41632199  0.07586790  1\\n  S  S69  1  0.04810086  0.86517467  0.27070662  1\\n  S  S70  1  0.46581041  0.32743582  0.30295133  1\\n  S  S71  1  0.40653232  0.74356167  0.19789502  1\\n  S  S72  1  0.41215958  0.31632784  0.72836662  1\\n  S  S73  1  0.19678936  0.87278570  0.49429127  1\\n  S  S74  1  0.84972071  0.31281442  0.65255936  1\\n  S  S75  1  0.72756583  0.83804117  1.02598683  1\\n  S  S76  1  -0.22001479  0.22782829  -0.04470457  1\\n  S  S77  1  -0.04728186  0.48824366  0.30900482  1\\n  S  S78  1  0.71789438  0.08224286  0.57264983  1\\n  S  S79  1  0.70045277  0.48703908  0.34957057  1\\n  S  S80  1  0.10445966  0.18295298  -0.39350954  1\\n  S  S81  1  0.12643257  0.58486720  -0.19232922  1\\n  S  S82  1  0.53984008  -0.05761253  -0.16352653  1\\n  S  S83  1  0.44788074  0.73827114  -0.05345723  1\\n  S  S84  1  0.21926343  0.33392437  1.25791729  1\\n  S  S85  1  0.55375498  0.89574775  1.22192983  1\\n  S  S86  1  0.92739099  0.30530795  1.13544770  1\\n  S  S87  1  1.09333163  0.89007646  1.02173737  1\\n  S  S88  1  0.58509322  0.40643542  0.54792537  1\\n  S  S89  1  0.46656500  0.89886745  0.53573384  1\\n  S  S90  1  1.06435828  0.41300231  0.58839368  1\\n  S  S91  1  1.04258104  1.28701755  0.41468017  1\\n  S  S92  1  0.05568546  0.15489895  0.20328039  1\\n  S  S93  1  -0.02560169  0.80003861  0.70911137  1\\n  S  S94  1  0.68319476  -0.00799383  0.33064196  1\\n  S  S95  1  0.56002313  0.58667993  0.55444476  1\\n  S  S96  1  0.32283479  0.08270967  0.69519696  1\\n  S  S97  1  0.22565603  0.43948401  1.01792433  1\\n  S  S98  1  0.62929294  0.11562738  0.75677379  1\\n  S  S99  1  0.67953598  0.69655159  0.84991098  1\\n  S  S100  1  0.35967118  0.13669742  0.17475341  1\\n  S  S101  1  0.28543236  0.91266443  0.29038879  1\\n  S  S102  1  0.64569918  0.28766275  0.19085207  1\\n  S  S103  1  0.92115294  1.03229721  0.15712901  1\\n  S  S104  1  0.30134379  0.36423188  0.48479557  1\\n  S  S105  1  0.25977860  0.83671564  0.73303923  1\\n  S  S106  1  0.86996511  0.55158551  0.65856072  1\\n  S  S107  1  0.71544703  0.78668000  0.61332168  1\\n  S  S108  1  0.24120223  0.14738122  0.38482539  1\\n  S  S109  1  0.22742074  0.56353459  0.31624388  1\\n  S  S110  1  0.97841933  0.08114068  0.44660919  1\\n  S  S111  1  0.46215195  0.59620302  0.30047322  1\\n  S  S112  1  0.40221952  0.30687108  0.05933100  1\\n  S  S113  1  0.34451563  0.93729516  0.04483047  1\\n  S  S114  1  0.53439168  0.34278164  -0.15069286  1\\n  S  S115  1  0.82675140  0.81982622  0.14177507  1\\n  S  S116  1  0.05830416  0.00822851  0.72427816  1\\n  S  S117  1  -0.00853703  0.56320328  1.01942778  1\\n  S  S118  1  0.82526139  -0.02889019  0.82864954  1\\n  S  S119  1  0.51649408  0.55232480  0.92183309  1\\n  S  S120  1  -0.13037132  0.17034041  0.40673920  1\\n  S  S121  1  0.02106941  0.67466640  0.27262697  1\\n  S  S122  1  0.53382295  0.13703062  0.39585049  1\\n  S  S123  1  0.71201275  0.68848856  0.37933783  1\\n  S  S124  1  0.39226531  0.49465006  0.69332725  1\\n  S  S125  1  0.36026839  0.71684278  0.51012391  1\\n  S  S126  1  1.01453754  0.42372602  0.84198356  1\\n  S  S127  1  0.90463523  0.89818501  0.53201761  1\\n\""]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["traj2 = Trajectory(\"md_out2.traj\")\n", "\n", "# get the non-charge-decorated structure\n", "third_structure = AseAtomsAdaptor.get_structure(traj2[-1])\n", "third_structure.to('equil.cif')"]}, {"cell_type": "code", "execution_count": 11, "id": "2bb830fd-6ba7-4c6e-98a0-6f27cc0a5d3e", "metadata": {}, "outputs": [], "source": ["# md3 = MolecularDynamics(\n", "#     atoms=third_structure,\n", "#     model=chgnet,\n", "#     ensemble=\"nvt\",\n", "#     temperature=300,  # in K\n", "#     starting_temperature=1200,\n", "#     timestep=2,  # in femto-seconds\n", "#     trajectory=\"md_out3.traj\",\n", "#     logfile=\"md_out3.log\",\n", "#     loginterval=100,\n", "# )\n", "# md3.run(1500)  # run a 3 ps MD simulation"]}, {"cell_type": "code", "execution_count": 12, "id": "5f32d1eb-ef5d-42f9-9c2a-a1dff83c8f45", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"# generated using pymatgen\\ndata_Li3PS4\\n_symmetry_space_group_name_H-M   'P 1'\\n_cell_length_a   12.34353186\\n_cell_length_b   16.03719718\\n_cell_length_c   13.03281824\\n_cell_angle_alpha   90.00000000\\n_cell_angle_beta   90.00000000\\n_cell_angle_gamma   90.00000000\\n_symmetry_Int_Tables_number   1\\n_chemical_formula_structural   Li3PS4\\n_chemical_formula_sum   'Li48 P16 S64'\\n_cell_volume   2579.92006255\\n_cell_formula_units_Z   16\\nloop_\\n _symmetry_equiv_pos_site_id\\n _symmetry_equiv_pos_as_xyz\\n  1  'x, y, z'\\nloop_\\n _atom_site_type_symbol\\n _atom_site_label\\n _atom_site_symmetry_multiplicity\\n _atom_site_fract_x\\n _atom_site_fract_y\\n _atom_site_fract_z\\n _atom_site_occupancy\\n  Li  Li0  1  -0.15339297  -0.25524457  1.04431841  1\\n  Li  Li1  1  -0.07150655  1.31368399  0.29551736  1\\n  Li  Li2  1  1.41787212  0.20704382  0.45496254  1\\n  Li  Li3  1  0.67448497  0.22848106  -0.04854399  1\\n  Li  Li4  1  0.45706501  -0.21983480  0.08183005  1\\n  Li  Li5  1  0.02150270  2.79408354  0.49756187  1\\n  Li  Li6  1  1.16944901  -0.16163910  -0.70615865  1\\n  Li  Li7  1  1.61738389  0.33719046  0.19079982  1\\n  Li  Li8  1  1.30993345  0.92377854  1.91645705  1\\n  Li  Li9  1  -0.09313841  1.02751925  0.94585750  1\\n  Li  Li10  1  1.02640084  0.66575777  1.16672650  1\\n  Li  Li11  1  1.11852656  1.25824915  1.10556599  1\\n  Li  Li12  1  -0.15497732  0.59980590  -0.40149609  1\\n  Li  Li13  1  -0.51536124  1.04931432  -0.33717897  1\\n  Li  Li14  1  0.50391953  -0.16696722  0.71061970  1\\n  Li  Li15  1  1.19227407  1.18325273  0.63171588  1\\n  Li  Li16  1  0.00241024  -0.75808201  -0.22090232  1\\n  Li  Li17  1  -0.33828426  -0.01352305  1.10594818  1\\n  Li  Li18  1  1.15324482  -0.53393852  0.71193199  1\\n  Li  Li19  1  0.42773187  1.13465420  0.15899315  1\\n  Li  Li20  1  -0.34408969  -0.28360552  -0.43353061  1\\n  Li  Li21  1  0.45966812  0.91572740  0.40950488  1\\n  Li  Li22  1  0.46394558  -0.43532377  0.08196355  1\\n  Li  Li23  1  0.81447678  1.10666238  0.26645054  1\\n  Li  Li24  1  0.38544698  0.64020685  -0.14342867  1\\n  Li  Li25  1  0.93749576  1.44663559  0.94636370  1\\n  Li  Li26  1  0.47986001  0.45247593  -0.65194636  1\\n  Li  Li27  1  1.12199093  0.92257465  0.06746221  1\\n  Li  Li28  1  1.68021274  0.40362581  0.52623217  1\\n  Li  Li29  1  0.40314209  0.57288736  0.63864263  1\\n  Li  Li30  1  1.69246136  0.61563500  1.12371560  1\\n  Li  Li31  1  0.23761412  1.30764169  0.85273840  1\\n  Li  Li32  1  1.11717102  -0.05153372  0.66140610  1\\n  Li  Li33  1  1.24741075  0.49128842  0.18654120  1\\n  Li  Li34  1  -1.32460678  0.83055148  -0.03892939  1\\n  Li  Li35  1  0.82540102  0.45909370  -0.83450151  1\\n  Li  Li36  1  -0.21215416  -0.11481723  0.43192482  1\\n  Li  Li37  1  -0.40825495  1.28824583  -0.32017229  1\\n  Li  Li38  1  -0.12591693  0.24738357  0.53539453  1\\n  Li  Li39  1  1.14768728  -0.25798116  1.80262604  1\\n  Li  Li40  1  0.97155903  0.55658133  0.38008933  1\\n  Li  Li41  1  0.41199925  1.09176156  -1.06363517  1\\n  Li  Li42  1  1.49151016  0.67178535  0.40680475  1\\n  Li  Li43  1  -0.17863938  1.04464187  -0.28609656  1\\n  Li  Li44  1  0.37269814  0.40674824  -0.02299320  1\\n  Li  Li45  1  0.64595411  0.45660210  -0.17021672  1\\n  Li  Li46  1  -0.82631958  0.40635384  0.47679879  1\\n  Li  Li47  1  0.15301575  1.06130972  1.36124600  1\\n  P  P48  1  0.13906835  0.08187519  0.80584601  1\\n  P  P49  1  0.17166710  0.56566879  0.96939156  1\\n  P  P50  1  0.61189677  0.03278954  0.87989266  1\\n  P  P51  1  0.60862632  0.68169853  0.85522142  1\\n  P  P52  1  0.38147816  0.30632429  0.19462161  1\\n  P  P53  1  0.34225156  0.94510569  0.16023198  1\\n  P  P54  1  0.85335336  0.28606345  0.07380728  1\\n  P  P55  1  0.92790451  0.88455017  0.23182870  1\\n  P  P56  1  0.38895273  0.38517614  0.60546394  1\\n  P  P57  1  0.32267630  0.78060727  0.56927458  1\\n  P  P58  1  0.90738386  0.43624987  0.68840111  1\\n  P  P59  1  0.89120681  0.78654548  0.65174944  1\\n  P  P60  1  0.14281015  0.23818399  0.36145382  1\\n  P  P61  1  0.20431566  0.65963402  0.36853938  1\\n  P  P62  1  0.61854819  0.04008171  0.45427057  1\\n  P  P63  1  0.67621428  0.55116756  0.32490054  1\\n  S  S64  1  0.57493649  0.09408342  1.01992969  1\\n  S  S65  1  0.58632085  0.58086075  0.94798925  1\\n  S  S66  1  1.11448900  0.18107641  0.90117468  1\\n  S  S67  1  1.28877534  0.62861646  1.04816381  1\\n  S  S68  1  -0.25504735  0.37896094  0.03061018  1\\n  S  S69  1  -0.00721353  0.90654000  0.37202658  1\\n  S  S70  1  0.42951437  0.43174390  0.17845261  1\\n  S  S71  1  0.26272284  0.84067822  0.09935601  1\\n  S  S72  1  0.45557161  0.44656315  0.73676423  1\\n  S  S73  1  0.21779146  0.86453806  0.51247382  1\\n  S  S74  1  0.79618813  0.33395214  0.65687362  1\\n  S  S75  1  0.68752139  0.75811273  1.12981855  1\\n  S  S76  1  -0.12288611  0.21301483  -0.05802643  1\\n  S  S77  1  0.15898183  0.53835003  0.33811287  1\\n  S  S78  1  0.68792249  -0.04213516  0.55482840  1\\n  S  S79  1  0.69486615  0.42236385  0.32472681  1\\n  S  S80  1  0.01680786  0.10151939  -0.30673554  1\\n  S  S81  1  0.19239511  0.59444723  -0.18472041  1\\n  S  S82  1  0.48200436  -0.02354754  -0.18671527  1\\n  S  S83  1  0.51044425  0.77051927  -0.10170450  1\\n  S  S84  1  0.20996563  0.31724243  1.26135621  1\\n  S  S85  1  0.49724347  0.91161437  1.14600224  1\\n  S  S86  1  0.96799369  0.36663578  1.12415994  1\\n  S  S87  1  1.02033027  0.81467979  1.11185923  1\\n  S  S88  1  0.51638784  0.33015058  0.53150307  1\\n  S  S89  1  0.48180331  0.80576585  0.52644887  1\\n  S  S90  1  0.98520616  0.50604114  0.57967238  1\\n  S  S91  1  1.02868550  1.31328309  0.43862971  1\\n  S  S92  1  0.05375905  0.16747048  0.26796670  1\\n  S  S93  1  0.02723587  0.71264514  0.64710606  1\\n  S  S94  1  0.64856839  0.01353302  0.30296619  1\\n  S  S95  1  0.65096761  0.58257334  0.47355020  1\\n  S  S96  1  0.29542125  0.11124053  0.76208435  1\\n  S  S97  1  0.17996835  0.44043886  0.99392347  1\\n  S  S98  1  0.61100385  0.13743622  0.78624408  1\\n  S  S99  1  0.76873684  0.72012286  0.86780233  1\\n  S  S100  1  0.49700784  0.24646256  0.27167002  1\\n  S  S101  1  0.30191724  0.95924602  0.31578368  1\\n  S  S102  1  0.78395689  0.23410151  0.20522933  1\\n  S  S103  1  0.89830565  0.99532713  0.15231541  1\\n  S  S104  1  0.25350980  0.32784852  0.66086257  1\\n  S  S105  1  0.31881941  0.82508774  0.71630388  1\\n  S  S106  1  0.80723677  0.50312130  0.77455530  1\\n  S  S107  1  0.83522757  0.73245984  0.51391414  1\\n  S  S108  1  0.22900824  0.16529422  0.46341331  1\\n  S  S109  1  0.32870462  0.71771457  0.30844430  1\\n  S  S110  1  0.84055883  0.10621807  0.46389737  1\\n  S  S111  1  0.54286895  0.59814954  0.23946593  1\\n  S  S112  1  0.34356420  0.26568086  0.05257231  1\\n  S  S113  1  0.28284226  1.02715989  0.06062707  1\\n  S  S114  1  0.51058445  0.34811807  -0.17316048  1\\n  S  S115  1  0.78902012  0.80839611  0.25231936  1\\n  S  S116  1  0.08908580  -0.03288443  0.86729653  1\\n  S  S117  1  0.01627274  0.61462949  0.99910931  1\\n  S  S118  1  0.75963557  -0.03200897  0.86961125  1\\n  S  S119  1  0.54940168  0.66831407  0.70043083  1\\n  S  S120  1  -0.31442578  0.15945351  0.47208142  1\\n  S  S121  1  0.05205107  0.70455513  0.34149254  1\\n  S  S122  1  0.45386368  0.05322843  0.48378916  1\\n  S  S123  1  0.82873327  0.58250662  0.27175371  1\\n  S  S124  1  0.36091112  0.47528670  0.49511620  1\\n  S  S125  1  0.26769093  0.65959671  0.52180339  1\\n  S  S126  1  1.02229720  0.37902037  0.79396780  1\\n  S  S127  1  0.93310898  0.91010205  0.64109749  1\\n\""]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["traj3 = Trajectory(\"md_out3.traj\")\n", "\n", "# get the non-charge-decorated structure\n", "final_structure = AseAtomsAdaptor.get_structure(traj3[-1])\n", "final_structure.to('quenched.cif')"]}, {"cell_type": "markdown", "id": "7c020dc4-8968-4ac1-80c3-d43e784400da", "metadata": {}, "source": ["# 에너지 비교"]}, {"cell_type": "code", "execution_count": 28, "id": "9aa21f76-0e6f-4ad7-b401-f653d1d0f29d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet will run on cuda\n", "NVT-Berendsen-MD created\n"]}], "source": ["md4 = MolecularDynamics(\n", "    atoms=final_structure,\n", "    model=chgnet,\n", "    ensemble=\"nvt\",\n", "    temperature=300,  # in K\n", "    starting_temperature=300,\n", "    timestep=2,  # in femto-seconds\n", "    trajectory=\"md_out4.traj\",\n", "    logfile=\"md_out4.log\",\n", "    loginterval=100,\n", ")\n", "md4.run(1500)  # run a 3 ps MD simulation"]}, {"cell_type": "code", "execution_count": 29, "id": "9a677b7a-d794-4fb7-9415-7dab7fc13873", "metadata": {}, "outputs": [], "source": ["traj4 = Trajectory(\"md_out4.traj\")\n", "\n", "fin_structures = [AseAtomsAdaptor.get_structure(atoms) for atoms in traj4]"]}, {"cell_type": "code", "execution_count": 30, "id": "98d5ef4a-5c1c-441b-b44a-553b33d8cf61", "metadata": {}, "outputs": [{"data": {"text/plain": ["16"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["len(fin_structures)"]}, {"cell_type": "code", "execution_count": 39, "id": "2e4d68f7-5e82-4adb-bb64-0b180e7de3df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on cuda\n", "CHGNet relaxed structure Full Formula (Li48 P16 S64)\n", "Reduced Formula: Li3PS4\n", "abc   :  12.344384  16.038108  13.033371\n", "angles:  90.000000  90.000000  90.000000\n", "pbc   :       True       True       True\n", "Sites (128)\n", "  #  SP            a          b          c       magmom\n", "---  ----  ---------  ---------  ---------  -----------\n", "  0  Li     0.194671   0.016837   0.667516  0.0111927\n", "  1  Li     0.194671   0.516838   0.667516  0.0111926\n", "  2  Li     0.694671   0.016838   0.667516  0.0111929\n", "  3  Li     0.694671   0.516838   0.667516  0.0111929\n", "  4  Li     0.305329   0.483162   0.332484  0.0111928\n", "  5  Li     0.305329   0.983162   0.332484  0.0111926\n", "  6  Li     0.805329   0.483163   0.332484  0.0111926\n", "  7  Li     0.805329   0.983163   0.332484  0.0111927\n", "  8  Li     0.444671   0.483163   0.832484  0.0111929\n", "  9  Li     0.444671   0.983162   0.832484  0.0111928\n", " 10  Li     0.944671   0.483163   0.832484  0.0111929\n", " 11  Li     0.944671   0.983163   0.832484  0.0111929\n", " 12  Li     0.055329   0.016837   0.167516  0.0111926\n", " 13  Li     0.055329   0.516838   0.167516  0.0111928\n", " 14  Li     0.555329   0.016838   0.167516  0.0111929\n", " 15  Li     0.555329   0.516838   0.167516  0.0111927\n", " 16  Li     0.305329   0.266837   0.332484  0.0111927\n", " 17  Li     0.305329   0.766837   0.332484  0.0111928\n", " 18  Li     0.805329   0.266837   0.332484  0.0111928\n", " 19  Li     0.805329   0.766837   0.332484  0.0111929\n", " 20  Li     0.194671   0.233162   0.667516  0.0111927\n", " 21  Li     0.194671   0.733162   0.667516  0.0111927\n", " 22  Li     0.694671   0.233163   0.667516  0.0111927\n", " 23  Li     0.694671   0.733162   0.667516  0.0111928\n", " 24  Li     0.055329   0.233162   0.167516  0.0111926\n", " 25  Li     0.055329   0.733163   0.167516  0.0111926\n", " 26  Li     0.555329   0.233162   0.167516  0.0111929\n", " 27  Li     0.555329   0.733162   0.167516  0.0111926\n", " 28  Li     0.444671   0.266838   0.832484  0.0111928\n", " 29  Li     0.444671   0.766837   0.832484  0.0111927\n", " 30  Li     0.944671   0.266837   0.832484  0.0111926\n", " 31  Li     0.944671   0.766838   0.832484  0.0111929\n", " 32  Li     0.25       0          0         0.000849783\n", " 33  Li     0.25       0.5        0         0.000849724\n", " 34  Li     0.75      -0         -0         0.000849605\n", " 35  Li     0.75       0.5        0         0.000849605\n", " 36  Li     0          0          0.5       0.000849545\n", " 37  Li     0          0.5        0.5       0.000849903\n", " 38  Li     0.5       -0          0.5       0.000849545\n", " 39  Li     0.5        0.5        0.5       0.000849724\n", " 40  Li     0.25       0.25       0         0.000849605\n", " 41  Li     0.25       0.75       0         0.000849664\n", " 42  Li     0.75       0.25       0         0.000849605\n", " 43  Li     0.75       0.75       0         0.000849724\n", " 44  Li     0          0.25       0.5       0.000849724\n", " 45  Li    -0          0.75       0.5       0.000849783\n", " 46  Li     0.5        0.25       0.5       0.000849485\n", " 47  Li     0.5        0.75       0.5       0.000849605\n", " 48  P      0.076396   0.125      0.910619  0.00584745\n", " 49  P      0.076396   0.625      0.910619  0.00584733\n", " 50  P      0.576396   0.125      0.910619  0.00584722\n", " 51  P      0.576396   0.625      0.910619  0.00584757\n", " 52  P      0.423604   0.375      0.089381  0.0058471\n", " 53  P      0.423604   0.875      0.089381  0.0058471\n", " 54  P      0.923604   0.375      0.089381  0.00584722\n", " 55  P      0.923604   0.875      0.089381  0.00584686\n", " 56  P      0.326396   0.375      0.589381  0.00584698\n", " 57  P      0.326396   0.875      0.589381  0.00584698\n", " 58  P      0.826396   0.375      0.589381  0.00584722\n", " 59  P      0.826396   0.875      0.589381  0.00584733\n", " 60  P      0.173604   0.125      0.410619  0.00584722\n", " 61  P      0.173604   0.625      0.410619  0.00584722\n", " 62  P      0.673604   0.125      0.410619  0.00584698\n", " 63  P      0.673604   0.625      0.410619  0.00584722\n", " 64  S      0.411454   0.125      0.900075  0.00834813\n", " 65  S      0.411454   0.625      0.900075  0.00834824\n", " 66  S      0.911454   0.125      0.900075  0.00834815\n", " 67  S      0.911454   0.625      0.900075  0.00834806\n", " 68  S      0.088546   0.375      0.099925  0.00834821\n", " 69  S      0.088546   0.875      0.099925  0.00834806\n", " 70  S      0.588546   0.375      0.099925  0.00834809\n", " 71  S      0.588546   0.875      0.099925  0.008348\n", " 72  S      0.161454   0.375      0.599925  0.00834808\n", " 73  S      0.161454   0.875      0.599925  0.00834806\n", " 74  S      0.661454   0.375      0.599925  0.00834811\n", " 75  S      0.661454   0.875      0.599925  0.00834814\n", " 76  S      0.338546   0.125      0.400075  0.00834799\n", " 77  S      0.338546   0.625      0.400075  0.00834812\n", " 78  S      0.838546   0.125      0.400075  0.00834813\n", " 79  S      0.838546   0.625      0.400075  0.00834826\n", " 80  S      0.136424   0.125      0.059847  0.018343\n", " 81  S      0.136424   0.625      0.059847  0.0183429\n", " 82  S      0.636424   0.125      0.059847  0.018343\n", " 83  S      0.636424   0.625      0.059847  0.0183429\n", " 84  S      0.363576   0.375      0.940153  0.0183431\n", " 85  S      0.363576   0.875      0.940153  0.0183429\n", " 86  S      0.863576   0.375      0.940153  0.0183431\n", " 87  S      0.863576   0.875      0.940153  0.018343\n", " 88  S      0.386424   0.375      0.440153  0.018343\n", " 89  S      0.386424   0.875      0.440153  0.0183431\n", " 90  S      0.886424   0.375      0.440153  0.018343\n", " 91  S      0.886424   0.875      0.440153  0.0183431\n", " 92  S      0.113576   0.125      0.559847  0.018343\n", " 93  S      0.113576   0.625      0.559847  0.0183431\n", " 94  S      0.613576   0.125      0.559847  0.018343\n", " 95  S      0.613576   0.625      0.559847  0.0183429\n", " 96  S      0.135719   0.017688   0.845906  0.012635\n", " 97  S      0.135719   0.517688   0.845906  0.0126351\n", " 98  S      0.635719   0.017688   0.845906  0.0126352\n", " 99  S      0.635719   0.517688   0.845906  0.0126352\n", "100  S      0.364281   0.482312   0.154094  0.0126351\n", "101  S      0.364281   0.982312   0.154094  0.0126352\n", "102  S      0.864281   0.482312   0.154094  0.0126352\n", "103  S      0.864281   0.982312   0.154094  0.0126352\n", "104  S      0.385719   0.482312   0.654094  0.0126353\n", "105  S      0.385719   0.982312   0.654094  0.0126352\n", "106  S      0.885719   0.482312   0.654094  0.0126349\n", "107  S      0.885719   0.982312   0.654094  0.012635\n", "108  S      0.114281   0.017688   0.345906  0.0126351\n", "109  S      0.114281   0.517688   0.345906  0.012635\n", "110  S      0.614281   0.017688   0.345906  0.0126351\n", "111  S      0.614281   0.517688   0.345906  0.012635\n", "112  S      0.364281   0.267688   0.154094  0.0126351\n", "113  S      0.364281   0.767688   0.154094  0.0126351\n", "114  S      0.864281   0.267688   0.154094  0.0126352\n", "115  S      0.864281   0.767688   0.154094  0.012635\n", "116  S      0.135719   0.232312   0.845906  0.012635\n", "117  S      0.135719   0.732312   0.845906  0.0126351\n", "118  S      0.635719   0.232312   0.845906  0.0126351\n", "119  S      0.635719   0.732312   0.845906  0.012635\n", "120  S      0.114281   0.232312   0.345906  0.0126352\n", "121  S      0.114281   0.732312   0.345906  0.0126352\n", "122  S      0.614281   0.232312   0.345906  0.0126351\n", "123  S      0.614281   0.732312   0.345906  0.0126351\n", "124  S      0.385719   0.267688   0.654094  0.0126351\n", "125  S      0.385719   0.767688   0.654094  0.0126352\n", "126  S      0.885719   0.267688   0.654094  0.0126351\n", "127  S      0.885719   0.767688   0.654094  0.0126352\n", "relaxed total energy in eV: -592.54638671875\n"]}], "source": ["from chgnet.model import StructOptimizer\n", "\n", "relaxer = StructOptimizer()\n", "result = relaxer.relax(cubic_structure, verbose=False)\n", "print(\"CHGNet relaxed structure\", result[\"final_structure\"])\n", "print(\"relaxed total energy in eV:\", result['trajectory'].energies[-1])"]}, {"cell_type": "code", "execution_count": 41, "id": "f3a1220f-2a8f-4544-a0b9-f84d1946e693", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# 경로 설정\n", "logfile = \"md_out4.log\"\n", "\n", "# 데이터 저장 리스트\n", "time_ps = []\n", "epot_eV = []\n", "\n", "# 파일 파싱\n", "with open(logfile, \"r\") as f:\n", "    lines = f.readlines()\n", "\n", "# 첫 줄은 헤더 → 건너뛰기\n", "for line in lines[1:]:\n", "    if line.strip():  # 공백 줄 제외\n", "        parts = line.split()\n", "        if len(parts) >= 3:\n", "            time = float(parts[0])       # Time[ps]\n", "            epot = float(parts[2])       # Epot[eV]\n", "            time_ps.append(time)\n", "            epot_eV.append(epot)\n", "\n", "# 시각화\n", "crystalline_energy = -592.54638671875  # 기준 에너지 [eV]\n", "\n", "plt.figure(figsize=(8, 5))\n", "plt.plot(time_ps, epot_eV, marker='o', linestyle='-', color='royalblue', label=\"MD Potential Energy\")\n", "\n", "# 기준선 추가\n", "plt.axhline(crystalline_energy, color='red', linestyle='--', label=\"Crystalline Energy\")\n", "# 그래프 꾸미기\n", "plt.xlabel(\"Time (ps)\")\n", "plt.ylabel(\"Potential Energy (eV)\")\n", "plt.title(\"CHGNet MD: Potential Energy vs Time\")\n", "plt.grid(True)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 45, "id": "f5ce14d4-ce79-40f0-a05e-68ab2b224923", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.07523934936523524 eV/atom difference between amourphous vs. crystalline\n"]}], "source": ["import numpy as np\n", "print((np.average(epot_eV) - crystalline_energy)/128, 'eV/atom difference between amourphous vs. crystalline')"]}, {"cell_type": "markdown", "id": "d78c061e-ad5f-4f47-ba4b-2c96bbbdee21", "metadata": {}, "source": ["# RDF 그리기"]}, {"cell_type": "code", "execution_count": 47, "id": "7544d71f-c6bd-48b5-b6d9-917108e36539", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:14: <PERSON><PERSON>taxWarning: invalid escape sequence '\\A'\n", "<>:14: <PERSON><PERSON>taxWarning: invalid escape sequence '\\A'\n", "/tmp/ipykernel_174140/3430180380.py:14: SyntaxWarning: invalid escape sequence '\\A'\n", "  plt.xlabel('Distance from Li ($\\AA$)')\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAioAAAG0CAYAAAActAwdAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAbsZJREFUeJzt3Xd4VGXax/HvzCSZ9AohlNB770pRUVHEvvYOgrruolJ2LbyufV2UXXeta8GCrqK4rrhrAUSaiDRp0kV6DyW9J3PeP05mkkCATDKd3+e6cs3kzJk5T0LI3Lmf+7kfi2EYBiIiIiIByOrvAYiIiIicjAIVERERCVgKVERERCRgKVARERGRgKVARURERAKWAhUREREJWApUREREJGCF+XsA9eFwONi/fz9xcXFYLBZ/D0dERERqwTAMcnNzadKkCVbrqXMmQR2o7N+/n/T0dH8PQ0REROpgz549NGvW7JTnBHWgEhcXB5hfaHx8vJ9HIyIiIrWRk5NDenq66338VII6UHFO98THxytQERERCTK1KdtQMa2IiIgELAUqIiIiErAUqIiIiEjACuoaFREREX8pLy+ntLTU38MISOHh4dhsNo+8lgIVERERNxiGwcGDB8nKyvL3UAJaYmIiaWlp9e5zpkBFRETEDc4gJTU1lejoaDUcPY5hGBQUFJCRkQFA48aN6/V6ClRERERqqby83BWkpKSk+Hs4ASsqKgqAjIwMUlNT6zUNpGJaERGRWnLWpERHR/t5JIHP+T2qbx2PAhURERE3abrn9Dz1PVKgIiIiIgFLgYqIiIgELAUqIiIiZziLxcIXX3zh72HUSIGKhKzCknJ/D0FEJGCMHDmSq6++usbHDhw4wPDhw0/5/BkzZnD22WeTkJBAXFwcXbp0Ydy4cZ4f6HEUqEhIev/HnXR9cjYLtmT4eygiIgEvLS0Nu91+0sfnzp3LjTfeyLXXXsvy5ctZuXIlzz77rE8686qPioSkH7cdodxhsGZPFkM6pPp7OCISwgzDoLDU9xncqHCbx1bWWCwWZsyYcdKMy5dffsmgQYN48MEHXcfat29/0vM9SYGKhKSM3GIA8orK/DwSEQl1haXldH58ts+vu/HpYURH+OZtPC0tjWnTprF+/Xq6du3qk2s6aepHQlJGTkWgUqxARUSkvu6//*********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pymatgen.analysis.diffusion.aimd.rdf import RadialDistributionFunctionFast\n", "\n", "def rdf_generate(structures, specie1, specie2):\n", "    rdf = RadialDistributionFunctionFast(structures)\n", "    graph = rdf.get_rdf(specie1, specie2)\n", "    return graph\n", "\n", "rdf1 = rdf_generate(fin_structures, 'Li', 'S')\n", "rdf2 = rdf_generate(fin_structures, 'Li', 'P')\n", "\n", "import matplotlib.pyplot as plt\n", "plt.plot(rdf1[0], rdf1[1], label=\"Li-S\")\n", "plt.plot(rdf2[0], rdf2[1], label=\"Li-P\")\n", "plt.xlabel('Distance from Li ($\\AA$)')\n", "plt.ylabel('# of atoms')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "950a50d0-7afd-4914-8ebf-58b75db5a2b8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}
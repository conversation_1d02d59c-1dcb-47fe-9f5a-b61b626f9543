from pymatgen.ext.matproj import MPRester  

API_KEY = "cwJruOX65TSz5C6KwWZTIj6BCHLoodVq"

with MPRester(API_KEY) as mpr:

    structure = mpr.get_structure_by_material_id('mp-985583', conventional_unit_cell=True)

    print(structure)

from pymatgen.transformations.advanced_transformations import CubicSupercellTransformation

# 1. 원하는 최소 길이 (단위: Å)
min_length = 9.0

# 2. Cubic supercell로 변환
transformer = CubicSupercellTransformation(min_length=min_length)
cubic_structure = transformer.apply_transformation(structure)

# 3. 결과 확인
print("✅ 생성된 supercell의 격자 길이:")
print(cubic_structure.lattice)

len(cubic_structure)

from pymatgen.io.vasp.sets import MPMDSet

# 타임스텝 및 총 스텝 수 설정
time_step_fs = 2  # fs
total_time_ps = 3  # ps
nsteps = int((total_time_ps * 1000) / time_step_fs)  # 15000 steps

# MPMDSet 입력 설정
md_set = MPMDSet(
    structure=cubic_structure,
    time_step=time_step_fs,
    nsteps=nsteps,
    start_temp=300,
    end_temp=1200
)

# 입력 파일 저장
md_set.write_input("MD_300K_to_1200K")
print(f"✅ MD 입력 생성 완료: 총 {nsteps} steps @ 2fs (총 {total_time_ps} ps)")

# # 타임스텝 및 총 스텝 수 설정
# time_step_fs = 2  # fs
# total_time_ps = 20  # ps
# nsteps = int((total_time_ps * 1000) / time_step_fs)  # 15000 steps

# # MPMDSet 입력 설정
# md_set = MPMDSet(
#     structure=second_structure,
#     time_step=time_step_fs,
#     nsteps=nsteps,
#     start_temp=1200,
#     end_temp=1200
# )

# # 입력 파일 저장
# md_set.write_input("MD_1200K_to_1200K")
# print(f"✅ MD 입력 생성 완료: 총 {nsteps} steps @ 2fs (총 {total_time_ps} ps)")

# # 타임스텝 및 총 스텝 수 설정
# time_step_fs = 2  # fs
# total_time_ps = 3  # ps
# nsteps = int((total_time_ps * 1000) / time_step_fs)  # 15000 steps

# # MPMDSet 입력 설정
# md_set = MPMDSet(
#     structure=thrid_structure,
#     time_step=time_step_fs,
#     nsteps=nsteps,
#     start_temp=1200,
#     end_temp=300
# )

# # 입력 파일 저장
# md_set.write_input("MD_1200K_to_300K")
# print(f"✅ MD 입력 생성 완료: 총 {nsteps} steps @ 2fs (총 {total_time_ps} ps)")

# from chgnet.model.model import CHGNet
# from chgnet.model.dynamics import MolecularDynamics
# import warnings
# warnings.filterwarnings("ignore", module="pymatgen")
# warnings.filterwarnings("ignore", module="ase")

# chgnet = CHGNet.load()

# md = MolecularDynamics(
#     atoms=cubic_structure,
#     model=chgnet,
#     ensemble="nvt",
#     temperature=1200,  # in K
#     starting_temperature=300,
#     timestep=2,  # in femto-seconds
#     trajectory="md_out.traj",
#     logfile="md_out.log",
#     loginterval=100,
# )
# md.run(1500)  # run a 3 ps MD simulation

from ase.io.trajectory import Trajectory
from pymatgen.io.ase import AseAtomsAdaptor

traj = Trajectory("md_out.traj")

# get the non-charge-decorated structure
second_structure = AseAtomsAdaptor.get_structure(traj[-1])
print(second_structure)

# md2 = MolecularDynamics(
#     atoms=second_structure,
#     model=chgnet,
#     ensemble="nvt",
#     temperature=1200,  # in K
#     starting_temperature=1200,
#     timestep=2,  # in femto-seconds
#     trajectory="md_out2.traj",
#     logfile="md_out2.log",
#     loginterval=100,
# )
# md2.run(5000)  # run a 10 ps MD simulation

traj2 = Trajectory("md_out2.traj")

# get the non-charge-decorated structure
third_structure = AseAtomsAdaptor.get_structure(traj2[-1])
third_structure.to('equil.cif')

# md3 = MolecularDynamics(
#     atoms=third_structure,
#     model=chgnet,
#     ensemble="nvt",
#     temperature=300,  # in K
#     starting_temperature=1200,
#     timestep=2,  # in femto-seconds
#     trajectory="md_out3.traj",
#     logfile="md_out3.log",
#     loginterval=100,
# )
# md3.run(1500)  # run a 3 ps MD simulation

traj3 = Trajectory("md_out3.traj")

# get the non-charge-decorated structure
final_structure = AseAtomsAdaptor.get_structure(traj3[-1])
final_structure.to('quenched.cif')

md4 = MolecularDynamics(
    atoms=final_structure,
    model=chgnet,
    ensemble="nvt",
    temperature=300,  # in K
    starting_temperature=300,
    timestep=2,  # in femto-seconds
    trajectory="md_out4.traj",
    logfile="md_out4.log",
    loginterval=100,
)
md4.run(1500)  # run a 3 ps MD simulation

traj4 = Trajectory("md_out4.traj")

fin_structures = [AseAtomsAdaptor.get_structure(atoms) for atoms in traj4]

len(fin_structures)

from chgnet.model import StructOptimizer

relaxer = StructOptimizer()
result = relaxer.relax(cubic_structure, verbose=False)
print("CHGNet relaxed structure", result["final_structure"])
print("relaxed total energy in eV:", result['trajectory'].energies[-1])

import matplotlib.pyplot as plt

# 경로 설정
logfile = "md_out4.log"

# 데이터 저장 리스트
time_ps = []
epot_eV = []

# 파일 파싱
with open(logfile, "r") as f:
    lines = f.readlines()

# 첫 줄은 헤더 → 건너뛰기
for line in lines[1:]:
    if line.strip():  # 공백 줄 제외
        parts = line.split()
        if len(parts) >= 3:
            time = float(parts[0])       # Time[ps]
            epot = float(parts[2])       # Epot[eV]
            time_ps.append(time)
            epot_eV.append(epot)

# 시각화
crystalline_energy = -592.54638671875  # 기준 에너지 [eV]

plt.figure(figsize=(8, 5))
plt.plot(time_ps, epot_eV, marker='o', linestyle='-', color='royalblue', label="MD Potential Energy")

# 기준선 추가
plt.axhline(crystalline_energy, color='red', linestyle='--', label="Crystalline Energy")
# 그래프 꾸미기
plt.xlabel("Time (ps)")
plt.ylabel("Potential Energy (eV)")
plt.title("CHGNet MD: Potential Energy vs Time")
plt.grid(True)
plt.legend()
plt.tight_layout()
plt.show()

import numpy as np
print((np.average(epot_eV) - crystalline_energy)/128, 'eV/atom difference between amourphous vs. crystalline')

from pymatgen.analysis.diffusion.aimd.rdf import RadialDistributionFunctionFast

def rdf_generate(structures, specie1, specie2):
    rdf = RadialDistributionFunctionFast(structures)
    graph = rdf.get_rdf(specie1, specie2)
    return graph

rdf1 = rdf_generate(fin_structures, 'Li', 'S')
rdf2 = rdf_generate(fin_structures, 'Li', 'P')

import matplotlib.pyplot as plt
plt.plot(rdf1[0], rdf1[1], label="Li-S")
plt.plot(rdf2[0], rdf2[1], label="Li-P")
plt.xlabel('Distance from Li ($\AA$)')
plt.ylabel('# of atoms')
plt.legend()
plt.show()


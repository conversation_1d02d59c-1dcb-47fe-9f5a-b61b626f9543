 running on    1 total cores
 distrk:  each k-point on    1 cores,    1 groups
 distr:  one band on    1 cores,    1 groups
 OpenACC runtime initialized ...    1 GPUs detected
 vasp.6.3.1 04May22 (build Mar 14 2023 15:17:25) complex                         
 POSCAR found type information on POSCAR LiO 
 POSCAR found :  2 types and      95 ions
 scaLAPACK will be used selectively (only on CPU)
 LDA part: xc-table for Pade appr. of Perdew
 POSCAR found type information on POSCAR LiO 
 POSCAR found :  2 types and      95 ions
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ... GRIDC
 FFT: planning ... GRID_SOFT
 FFT: planning ... GRID
 WAVECAR not read
 WARNING: random wavefunctions but no delay for mixing, default for NELMDL
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.456590492879E+04    0.45659E+04   -0.20152E+05  1104   0.158E+03
DAV:   2     0.955633194377E+02   -0.44703E+04   -0.42120E+04  1080   0.475E+02
DAV:   3    -0.445163490111E+03   -0.54073E+03   -0.52643E+03  1268   0.182E+02
DAV:   4    -0.469767738377E+03   -0.24604E+02   -0.24401E+02  1232   0.401E+01
DAV:   5    -0.470296726155E+03   -0.52899E+00   -0.52806E+00  1280   0.672E+00BRMIX: very serious problems
 the old and the new charge density differ
 old charge density:   378.00000 new  379.00002
    0.595E+01
RMM:   6    -0.425801111377E+03    0.44496E+02   -0.24380E+02  1188   0.262E+01    0.226E+01
RMM:   7    -0.436788575319E+03   -0.10987E+02   -0.23708E+01  1178   0.942E+00    0.123E+01
RMM:   8    -0.438488867843E+03   -0.17003E+01   -0.45163E+00  1261   0.436E+00    0.752E+00
RMM:   9    -0.439856399084E+03   -0.13675E+01   -0.35167E+00  1075   0.440E+00    0.261E+00
RMM:  10    -0.439753030007E+03    0.10337E+00   -0.30877E-01  1174   0.171E+00    0.149E+00
RMM:  11    -0.439744670521E+03    0.83595E-02   -0.45000E-01  1146   0.106E+00    0.649E-01
RMM:  12    -0.439786745943E+03   -0.42075E-01   -0.15233E-01  1120   0.619E-01    0.123E+00
RMM:  13    -0.439751104085E+03    0.35642E-01   -0.98809E-02  1016   0.508E-01    0.182E-01
RMM:  14    -0.439754958889E+03   -0.38548E-02   -0.36118E-02  1060   0.307E-01
   1 F= -.43975496E+03 E0= -.43974029E+03  d E =-.439755E+03  mag=     1.0089
 curvature:   0.00 expect dE= 0.000E+00 dE for cont linesearch  0.000E+00
 trial: gam= 0.00000 g(F)=  0.889E-01 g(S)=  0.233E+00 ort = 0.000E+00 (trialstep = 0.100E+01)
 search vector abs. value=  0.322E+00
 bond charge predicted
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1    -0.440634452687E+03   -0.88335E+00   -0.27051E+01  1280   0.145E+01    0.659E+00
RMM:   2    -0.439911787273E+03    0.72267E+00   -0.28879E+00  1130   0.373E+00    0.162E+00
RMM:   3    -0.439941108834E+03   -0.29322E-01   -0.49925E-01  1286   0.177E+00    0.948E-01
RMM:   4    -0.439951075274E+03   -0.99664E-02   -0.26407E-01  1181   0.996E-01    0.104E+00
RMM:   5    -0.439924698524E+03    0.26377E-01   -0.34468E-02   966   0.540E-01    0.417E-01
RMM:   6    -0.439925079847E+03   -0.38132E-03   -0.57487E-02  1038   0.395E-01    0.244E-01
RMM:   7    -0.439924584651E+03    0.49520E-03   -0.37766E-03   672   0.158E-01
   2 F= -.43992458E+03 E0= -.43990755E+03  d E =-.169626E+00  mag=     1.0000
 trial-energy change:   -0.169626  1 .order   -0.175993   -0.322073   -0.029913
 step:   1.1024(harm=  1.1024)  dis= 0.01057  next Energy=  -439.932483 (dE=-0.178E+00)
 bond charge predicted
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1    -0.439939172632E+03   -0.14093E-01   -0.31855E-01  1240   0.153E+00    0.660E-01
RMM:   2    -0.439932036888E+03    0.71357E-02   -0.30318E-02  1015   0.395E-01    0.164E-01
RMM:   3    -0.439932308262E+03   -0.27137E-03   -0.46336E-03   637   0.177E-01
   3 F= -.43993231E+03 E0= -.43991490E+03  d E =-.177349E+00  mag=     1.0000
 curvature:  -0.55 expect dE=-0.189E-01 dE for cont linesearch -0.669E-02
 trial: gam=-0.08769 g(F)=  0.338E-01 g(S)=  0.449E-03 ort = 0.625E-01 (trialstep = 0.102E+01)
 search vector abs. value=  0.258E-01
 reached required accuracy - stopping structural energy minimisation

{"cells": [{"cell_type": "markdown", "id": "80dc7446-23bd-4c88-9e6e-a2613ca8d999", "metadata": {}, "source": ["# Materials Project에서 구조 가져오기 및 cubic supercell 만들기"]}, {"cell_type": "code", "execution_count": 1, "id": "e8d21ab0-fa51-4d45-b0e1-6fe95e0e8940", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Full Formula (Li8 O4)\n", "Reduced Formula: Li2O\n", "abc   :   4.653709   4.653709   4.653709\n", "angles:  90.000000  90.000000  90.000000\n", "pbc   :       True       True       True\n", "Sites (12)\n", "  #  SP       a     b     c\n", "---  ----  ----  ----  ----\n", "  0  Li    0.25  0.75  0.25\n", "  1  Li    0.75  0.25  0.75\n", "  2  Li    0.25  0.25  0.75\n", "  3  Li    0.75  0.75  0.25\n", "  4  Li    0.75  0.75  0.75\n", "  5  Li    0.25  0.25  0.25\n", "  6  Li    0.75  0.25  0.25\n", "  7  Li    0.25  0.75  0.75\n", "  8  O     0     0     0\n", "  9  O     0     0.5   0.5\n", " 10  O     0.5   0     0.5\n", " 11  O     0.5   0.5   0\n"]}], "source": ["from pymatgen.ext.matproj import MPRester  \n", "\n", "API_KEY = \"NlY7ehbJQwKy9ZTmXlrpz46SMY6HZOpO\"\n", "\n", "with MPRester(API_KEY) as mpr:\n", "\n", "    structure = mpr.get_structure_by_material_id('mp-1960', conventional_unit_cell=True)\n", "\n", "    print(structure)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "89ef9518-37ec-40fa-b361-d3dcd153b37b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 생성된 supercell의 격자 길이:\n", "9.307418 0.000000 0.000000\n", "0.000000 9.307418 0.000000\n", "0.000000 0.000000 9.307418\n"]}], "source": ["from pymatgen.transformations.advanced_transformations import CubicSupercellTransformation\n", "\n", "# 1. 원하는 최소 길이 (단위: Å)\n", "min_length = 9.0\n", "\n", "# 2. <PERSON><PERSON><PERSON> supercell로 변환\n", "transformer = CubicSupercellTransformation(min_length=min_length)\n", "cubic_structure = transformer.apply_transformation(structure)\n", "\n", "# 3. 결과 확인\n", "print(\"✅ 생성된 supercell의 격자 길이:\")\n", "print(cubic_structure.lattice)"]}, {"cell_type": "code", "execution_count": 3, "id": "5c0301bc-e1d6-4ed7-bec4-a154d3d3885f", "metadata": {}, "outputs": [], "source": ["# perfect한 구조의 계산 input 생성\n", "from pymatgen.io.vasp.sets import MPRelaxSet\n", "vasp_input_set = MPRelaxSet(cubic_structure)\n", "dirname = f\"Li2O_perfect\"\n", "vasp_input_set.write_input(dirname)"]}, {"cell_type": "markdown", "id": "f2d03e8f-e0d8-46bd-a03b-ba9c917dbf66", "metadata": {}, "source": ["# Li vacancy 및 O vacancy를 가진 구조 생성"]}, {"cell_type": "code", "execution_count": 4, "id": "36ad7fa0-9aa2-4b98-b114-a3b647091a33", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["Structure Summary\n", "<PERSON><PERSON><PERSON>\n", "    abc : 9.307417527672138 9.307417527672138 9.307417527672138\n", " angles : 90.0 90.0 90.0\n", " volume : 806.2831613632977\n", "      A : np.float64(9.307417527672138) np.float64(0.0) np.float64(5.699149541795828e-16)\n", "      B : np.float64(1.496746893624689e-15) np.float64(9.307417527672138) np.float64(5.699149541795828e-16)\n", "      C : np.float64(0.0) np.float64(0.0) np.float64(9.307417527672138)\n", "    pbc : True True True\n", "PeriodicSite: Li (1.163, 3.49, 1.163) [0.125, 0.375, 0.125]\n", "PeriodicSite: Li (1.163, 3.49, 5.817) [0.125, 0.375, 0.625]\n", "PeriodicSite: Li (1.163, 8.144, 1.163) [0.125, 0.875, 0.125]\n", "PeriodicSite: Li (1.163, 8.144, 5.817) [0.125, 0.875, 0.625]\n", "PeriodicSite: Li (5.817, 3.49, 1.163) [0.625, 0.375, 0.125]\n", "PeriodicSite: Li (5.817, 3.49, 5.817) [0.625, 0.375, 0.625]\n", "PeriodicSite: Li (5.817, 8.144, 1.163) [0.625, 0.875, 0.125]\n", "PeriodicSite: Li (5.817, 8.144, 5.817) [0.625, 0.875, 0.625]\n", "PeriodicSite: Li (3.49, 1.163, 3.49) [0.375, 0.125, 0.375]\n", "PeriodicSite: Li (3.49, 1.163, 8.144) [0.375, 0.125, 0.875]\n", "PeriodicSite: Li (3.49, 5.817, 3.49) [0.375, 0.625, 0.375]\n", "PeriodicSite: Li (3.49, 5.817, 8.144) [0.375, 0.625, 0.875]\n", "PeriodicSite: Li (8.144, 1.163, 3.49) [0.875, 0.125, 0.375]\n", "PeriodicSite: Li (8.144, 1.163, 8.144) [0.875, 0.125, 0.875]\n", "PeriodicSite: Li (8.144, 5.817, 3.49) [0.875, 0.625, 0.375]\n", "PeriodicSite: Li (8.144, 5.817, 8.144) [0.875, 0.625, 0.875]\n", "PeriodicSite: Li (1.163, 1.163, 3.49) [0.125, 0.125, 0.375]\n", "PeriodicSite: Li (1.163, 1.163, 8.144) [0.125, 0.125, 0.875]\n", "PeriodicSite: Li (1.163, 5.817, 3.49) [0.125, 0.625, 0.375]\n", "PeriodicSite: Li (1.163, 5.817, 8.144) [0.125, 0.625, 0.875]\n", "PeriodicSite: Li (5.817, 1.163, 3.49) [0.625, 0.125, 0.375]\n", "PeriodicSite: Li (5.817, 1.163, 8.144) [0.625, 0.125, 0.875]\n", "PeriodicSite: Li (5.817, 5.817, 3.49) [0.625, 0.625, 0.375]\n", "PeriodicSite: Li (5.817, 5.817, 8.144) [0.625, 0.625, 0.875]\n", "PeriodicSite: Li (3.49, 3.49, 1.163) [0.375, 0.375, 0.125]\n", "PeriodicSite: Li (3.49, 3.49, 5.817) [0.375, 0.375, 0.625]\n", "PeriodicSite: Li (3.49, 8.144, 1.163) [0.375, 0.875, 0.125]\n", "PeriodicSite: Li (3.49, 8.144, 5.817) [0.375, 0.875, 0.625]\n", "PeriodicSite: Li (8.144, 3.49, 1.163) [0.875, 0.375, 0.125]\n", "PeriodicSite: Li (8.144, 3.49, 5.817) [0.875, 0.375, 0.625]\n", "PeriodicSite: Li (8.144, 8.144, 1.163) [0.875, 0.875, 0.125]\n", "PeriodicSite: Li (8.144, 8.144, 5.817) [0.875, 0.875, 0.625]\n", "PeriodicSite: Li (3.49, 3.49, 3.49) [0.375, 0.375, 0.375]\n", "PeriodicSite: Li (3.49, 3.49, 8.144) [0.375, 0.375, 0.875]\n", "PeriodicSite: Li (3.49, 8.144, 3.49) [0.375, 0.875, 0.375]\n", "PeriodicSite: Li (3.49, 8.144, 8.144) [0.375, 0.875, 0.875]\n", "PeriodicSite: Li (8.144, 3.49, 3.49) [0.875, 0.375, 0.375]\n", "PeriodicSite: Li (8.144, 3.49, 8.144) [0.875, 0.375, 0.875]\n", "PeriodicSite: Li (8.144, 8.144, 3.49) [0.875, 0.875, 0.375]\n", "PeriodicSite: Li (8.144, 8.144, 8.144) [0.875, 0.875, 0.875]\n", "PeriodicSite: Li (1.163, 1.163, 1.163) [0.125, 0.125, 0.125]\n", "PeriodicSite: Li (1.163, 1.163, 5.817) [0.125, 0.125, 0.625]\n", "PeriodicSite: Li (1.163, 5.817, 1.163) [0.125, 0.625, 0.125]\n", "PeriodicSite: Li (1.163, 5.817, 5.817) [0.125, 0.625, 0.625]\n", "PeriodicSite: Li (5.817, 1.163, 1.163) [0.625, 0.125, 0.125]\n", "PeriodicSite: Li (5.817, 1.163, 5.817) [0.625, 0.125, 0.625]\n", "PeriodicSite: Li (5.817, 5.817, 1.163) [0.625, 0.625, 0.125]\n", "PeriodicSite: Li (5.817, 5.817, 5.817) [0.625, 0.625, 0.625]\n", "PeriodicSite: Li (3.49, 1.163, 1.163) [0.375, 0.125, 0.125]\n", "PeriodicSite: Li (3.49, 1.163, 5.817) [0.375, 0.125, 0.625]\n", "PeriodicSite: Li (3.49, 5.817, 1.163) [0.375, 0.625, 0.125]\n", "PeriodicSite: Li (3.49, 5.817, 5.817) [0.375, 0.625, 0.625]\n", "PeriodicSite: Li (8.144, 1.163, 1.163) [0.875, 0.125, 0.125]\n", "PeriodicSite: Li (8.144, 1.163, 5.817) [0.875, 0.125, 0.625]\n", "PeriodicSite: Li (8.144, 5.817, 1.163) [0.875, 0.625, 0.125]\n", "PeriodicSite: Li (8.144, 5.817, 5.817) [0.875, 0.625, 0.625]\n", "PeriodicSite: Li (1.163, 3.49, 3.49) [0.125, 0.375, 0.375]\n", "PeriodicSite: Li (1.163, 3.49, 8.144) [0.125, 0.375, 0.875]\n", "PeriodicSite: Li (1.163, 8.144, 3.49) [0.125, 0.875, 0.375]\n", "PeriodicSite: Li (1.163, 8.144, 8.144) [0.125, 0.875, 0.875]\n", "PeriodicSite: Li (5.817, 3.49, 3.49) [0.625, 0.375, 0.375]\n", "PeriodicSite: Li (5.817, 3.49, 8.144) [0.625, 0.375, 0.875]\n", "PeriodicSite: Li (5.817, 8.144, 3.49) [0.625, 0.875, 0.375]\n", "PeriodicSite: Li (5.817, 8.144, 8.144) [0.625, 0.875, 0.875]\n", "PeriodicSite: O (0.0, 0.0, 4.654) [0.0, 0.0, 0.5]\n", "PeriodicSite: O (7.484e-16, 4.654, 9.307) [0.0, 0.5, 1.0]\n", "PeriodicSite: O (7.484e-16, 4.654, 4.654) [0.0, 0.5, 0.5]\n", "PeriodicSite: O (4.654, 0.0, 2.85e-16) [0.5, 0.0, 0.0]\n", "PeriodicSite: O (4.654, 0.0, 4.654) [0.5, 0.0, 0.5]\n", "PeriodicSite: O (4.654, 4.654, 9.307) [0.5, 0.5, 1.0]\n", "PeriodicSite: O (4.654, 4.654, 4.654) [0.5, 0.5, 0.5]\n", "PeriodicSite: O (3.742e-16, 2.327, 2.327) [0.0, 0.25, 0.25]\n", "PeriodicSite: O (3.742e-16, 2.327, 6.981) [0.0, 0.25, 0.75]\n", "PeriodicSite: O (1.123e-15, 6.981, 2.327) [0.0, 0.75, 0.25]\n", "PeriodicSite: O (1.123e-15, 6.981, 6.981) [0.0, 0.75, 0.75]\n", "PeriodicSite: O (4.654, 2.327, 2.327) [0.5, 0.25, 0.25]\n", "PeriodicSite: O (4.654, 2.327, 6.981) [0.5, 0.25, 0.75]\n", "PeriodicSite: O (4.654, 6.981, 2.327) [0.5, 0.75, 0.25]\n", "PeriodicSite: O (4.654, 6.981, 6.981) [0.5, 0.75, 0.75]\n", "PeriodicSite: O (2.327, 0.0, 2.327) [0.25, 0.0, 0.25]\n", "PeriodicSite: O (2.327, 0.0, 6.981) [0.25, 0.0, 0.75]\n", "PeriodicSite: O (2.327, 4.654, 2.327) [0.25, 0.5, 0.25]\n", "PeriodicSite: O (2.327, 4.654, 6.981) [0.25, 0.5, 0.75]\n", "PeriodicSite: O (6.981, 0.0, 2.327) [0.75, 0.0, 0.25]\n", "PeriodicSite: O (6.981, 0.0, 6.981) [0.75, 0.0, 0.75]\n", "PeriodicSite: O (6.981, 4.654, 2.327) [0.75, 0.5, 0.25]\n", "PeriodicSite: O (6.981, 4.654, 6.981) [0.75, 0.5, 0.75]\n", "PeriodicSite: O (2.327, 2.327, 9.307) [0.25, 0.25, 1.0]\n", "PeriodicSite: O (2.327, 2.327, 4.654) [0.25, 0.25, 0.5]\n", "PeriodicSite: O (2.327, 6.981, 9.307) [0.25, 0.75, 1.0]\n", "PeriodicSite: O (2.327, 6.981, 4.654) [0.25, 0.75, 0.5]\n", "PeriodicSite: O (6.981, 2.327, 5.699e-16) [0.75, 0.25, 0.0]\n", "PeriodicSite: O (6.981, 2.327, 4.654) [0.75, 0.25, 0.5]\n", "PeriodicSite: O (6.981, 6.981, 9.307) [0.75, 0.75, 1.0]\n", "PeriodicSite: O (6.981, 6.981, 4.654) [0.75, 0.75, 0.5]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Li와 O 원자의 인덱스 각각 찾기\n", "li_indices = [i for i, site in enumerate(cubic_structure) if site.species_string == \"Li\"]\n", "o_indices = [i for i, site in enumerate(cubic_structure) if site.species_string == \"O\"]\n", "\n", "# 첫 번째 Li vacancy 구조\n", "vac_li = cubic_structure.copy()\n", "vac_li.remove_sites([li_indices[0]])\n", "\n", "# 첫 번째 O vacancy 구조\n", "vac_o = cubic_structure.copy()\n", "vac_o.remove_sites([o_indices[0]])"]}, {"cell_type": "code", "execution_count": 5, "id": "2634e59d-f125-483a-b2be-b9461da3053c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Li2O_vac_Li_charge_+0/ (NELECT=381.0) 생성 완료\n", "✅ Li2O_vac_Li_charge_+1/ (NELECT=380.0) 생성 완료\n", "✅ Li2O_vac_Li_charge_-1/ (NELECT=382.0) 생성 완료\n"]}], "source": ["from pymatgen.io.vasp.sets import MPRelaxSet\n", "\n", "# 1. 기준 전자수 계산 (POTCAR 기준 ZVAL)\n", "base_input = MPRelaxSet(vac_li)\n", "zvals = [p.nelectrons for p in base_input.potcar]\n", "atom_counts = base_input.structure.composition.get_el_amt_dict()\n", "elements = [str(p.element) for p in base_input.potcar]\n", "\n", "base_nelect = sum(zval * atom_counts[el] for zval, el in zip(zvals, elements))\n", "\n", "# 2. 전하 상태별 input 생성\n", "for charge in [0, +1, -1]:\n", "    nelect = base_nelect - charge\n", "    vasp_input_set = MPRelaxSet(\n", "        vac_li,\n", "        user_incar_settings={\"NELECT\": nelect}\n", "    )\n", "    dirname = f\"Li2O_vac_Li_charge_{charge:+d}\"\n", "    vasp_input_set.write_input(dirname)\n", "    print(f\"✅ {dirname}/ (NELECT={nelect}) 생성 완료\")"]}, {"cell_type": "code", "execution_count": 6, "id": "0995a520-04b8-4eaf-a54d-a7ebaeb89d2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Li2O_vac_O_charge_+0/ (NELECT=378.0) 생성 완료\n", "✅ Li2O_vac_O_charge_+1/ (NELECT=377.0) 생성 완료\n", "✅ Li2O_vac_O_charge_-1/ (NELECT=379.0) 생성 완료\n"]}], "source": ["# 1. 기준 전자수 계산 (POTCAR 기준 ZVAL)\n", "base_input = MPRelaxSet(vac_o)\n", "zvals = [p.nelectrons for p in base_input.potcar]\n", "atom_counts = base_input.structure.composition.get_el_amt_dict()\n", "elements = [str(p.element) for p in base_input.potcar]\n", "\n", "base_nelect = sum(zval * atom_counts[el] for zval, el in zip(zvals, elements))\n", "\n", "# 2. 전하 상태별 input 생성\n", "for charge in [0, +1, -1]:\n", "    nelect = base_nelect - charge\n", "    vasp_input_set = MPRelaxSet(\n", "        vac_o,\n", "        user_incar_settings={\"NELECT\": nelect}\n", "    )\n", "    dirname = f\"Li2O_vac_O_charge_{charge:+d}\"\n", "    vasp_input_set.write_input(dirname)\n", "    print(f\"✅ {dirname}/ (NELECT={nelect}) 생성 완료\")"]}, {"cell_type": "markdown", "id": "b9da83d4-c6e6-45d2-b4f7-49dae3df065a", "metadata": {}, "source": ["# 계산결과 받아오기"]}, {"cell_type": "code", "execution_count": 20, "id": "9319eb19-703c-4d21-bbd8-256c0984d0a0", "metadata": {}, "outputs": [], "source": ["# from pymatgen.io.vasp.outputs import Oszicar\n", "\n", "# E_pristine = Oszicar('./Li2O_perfect/OSZICAR').final_energy\n", "# E_vac_Li_n1 = Oszicar('./Li2O_vac_Li_charge_-1/OSZICAR').final_energy\n", "# E_vac_Li_0 = Oszicar('./Li2O_vac_Li_charge_+0/OSZICAR').final_energy\n", "# E_vac_Li_p1 = Oszicar('./Li2O_vac_Li_charge_+1/OSZICAR').final_energy\n", "# E_vac_O_n1 = Oszicar('./Li2O_vac_O_charge_-1/OSZICAR').final_energy\n", "# E_vac_O_0 = Oszicar('./Li2O_vac_O_charge_+0/OSZICAR').final_energy\n", "# E_vac_O_p1 = Oszicar('./Li2O_vac_O_charge_+1/OSZICAR').final_energy"]}, {"cell_type": "code", "execution_count": null, "id": "0e61aa13-ff3e-40bf-b674-51f40de382b9", "metadata": {}, "outputs": [], "source": ["# 직접 계산 불가능한 경우: 미리 계산한 결과 에너지\n", "E_pristine = -456.43325 \n", "E_vac_Li_n1 = -451.26919 \n", "E_vac_Li_0 = -450.98199 \n", "E_vac_Li_p1 = -450.75324 \n", "E_vac_O_n1 = -439.9149 \n", "E_vac_O_0 = -444.88152 \n", "E_vac_O_p1 = -447.33636 "]}, {"cell_type": "code", "execution_count": 11, "id": "7299a33a-e986-400d-9cea-1c4a26a76d8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E_pristine = -456.43325 eV\n", "E_vac_Li_n1 = -451.26919 eV\n", "E_vac_Li_0 = -450.98199 eV\n", "E_vac_Li_p1 = -450.75324 eV\n", "E_vac_O_n1 = -439.9149 eV\n", "E_vac_O_0 = -444.88152 eV\n", "E_vac_O_p1 = -447.33636 eV\n"]}], "source": ["print(f'E_pristine = {E_pristine}')\n", "print(f'E_vac_Li_n1 = {E_vac_Li_n1}')\n", "print(f'E_vac_Li_0 = {E_vac_Li_0}')\n", "print(f'E_vac_Li_p1 = {E_vac_Li_p1}')\n", "print(f'E_vac_O_n1 = {E_vac_O_n1}')\n", "print(f'E_vac_O_0 = {E_vac_O_0}')\n", "print(f'E_vac_O_p1 = {E_vac_O_p1}')"]}, {"cell_type": "code", "execution_count": 16, "id": "ec9e3aa6-b8cf-4af9-a77e-d18996648fb1", "metadata": {}, "outputs": [], "source": ["from pymatgen.entries.computed_entries import ComputedEntry\n", "from pymatgen.analysis.phase_diagram import PhaseDiagram, PDEntry\n", "from pymatgen.core import Element\n", "\n", "def compute_formation_energy(\n", "    defect_structure,\n", "    pristine_structure_energy,\n", "    defect_structure_energy,\n", "    element,\n", "    charge,\n", "    phase_entries,\n", "    fermi_level=0.0,\n", "    vbm=0.0\n", "):\n", "    \"\"\"\n", "    결함 형성 에너지 계산 함수 (pymatgen만 사용)\n", "    \n", "    Parameters:\n", "        defect_structure: pymatgen Structure with a defect\n", "        pristine_structure_energy: float\n", "        defect_structure_energy: float\n", "        element: str, removed element (e.g., 'Li' or 'O')\n", "        charge: int\n", "        phase_entries: list of PDEntry or ComputedEntry from Materials Project\n", "        fermi_level: float (default 0.0)\n", "        vbm: float, valence band maximum (default 0.0)\n", "        \n", "    Returns:\n", "        formation_energy: float\n", "    \"\"\"\n", "    # Step 1. Phase diagram으로부터 화학 퍼텐셜 (최소 안정한 영역) 구하기\n", "    pd = PhaseDiagram(phase_entries)\n", "    comp = defect_structure.composition.reduced_formula\n", "    chempots = pd.get_composition_chempots(defect_structure.composition)\n", "    mu_i = chempots[Element(element)]\n", "    \n", "    # Step 2. 결함 항 계산: +mu_i (원자 제거 시), -mu_i (원자 삽입 시)\n", "    n_removed = 1  # 이 예에서는 단일 원자 vacancy 기준\n", "    delta_mu = - n_removed * mu_i\n", "    \n", "    # Step 3. 총 형성 에너지 계산\n", "    E_form = (\n", "        defect_structure_energy\n", "        - pristine_structure_energy\n", "        + delta_mu\n", "        + charge * (fermi_level + vbm)\n", "    )\n", "    return E_form, element, charge"]}, {"cell_type": "code", "execution_count": 17, "id": "e43fea37-fe9e-4db8-b30f-1363830df75b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔋 Formation energy of Li vacancy (q=-1.0): 8.9574 eV\n", "🔋 Formation energy of Li vacancy (q=0): 10.2446 eV\n", "🔋 Formation energy of Li vacancy (q=1.0): 11.4734 eV\n", "🔋 Formation energy of O vacancy (q=-1.0): 26.6511 eV\n", "🔋 Formation energy of O vacancy (q=0): 22.6845 eV\n", "🔋 Formation energy of O vacancy (q=1.0): 21.2296 eV\n"]}], "source": ["from pymatgen.ext.matproj import MPRester\n", "\n", "# MPRester로 phase entries 가져오기\n", "\n", "with MPRester(API_KEY) as mpr:\n", "    phase_entries = mpr.get_entries_in_chemsys([\"Li\", \"O\"])\n", "\n", "# 함수 호출\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_li,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_Li_n1,\n", "    element=\"Li\",\n", "    charge=-1.0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_li,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_Li_0,\n", "    element=\"Li\",\n", "    charge=0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_li,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_Li_p1,\n", "    element=\"Li\",\n", "    charge=1.0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_o,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_O_n1,\n", "    element=\"O\",\n", "    charge=-1.0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_o,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_O_0,\n", "    element=\"O\",\n", "    charge=0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_o,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_O_p1,\n", "    element=\"O\",\n", "    charge=1.0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")"]}, {"cell_type": "markdown", "id": "ac272c2e-29f5-4fe4-9b9a-472797cd1616", "metadata": {}, "source": ["# Fermi level에 따른 결함생성에너지 그래프 그리기"]}, {"cell_type": "code", "execution_count": 12, "id": "b8b5b76b-acb8-4ec1-85a9-20a4bf2bb05c", "metadata": {}, "outputs": [], "source": ["E_vac_Li_dict = {-1:E_vac_Li_n1, 0:E_vac_Li_0, +1:E_vac_Li_p1}\n", "E_vac_O_dict = {-1:E_vac_O_n1, 0:E_vac_O_0, +1:E_vac_O_p1}"]}, {"cell_type": "code", "execution_count": 18, "id": "b64ef688-8083-4bb8-a3e6-5c7ab9d94288", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 예시 범위: 0 ~ 5 eV\n", "fermi_levels = np.linspace(0, 5.0, 100)\n", "\n", "# 각 charge state에 대한 형성 에너지 계산 함수 호출 (미리 정의된 함수 활용)\n", "defect_Li_energies = {}\n", "defect_O_energies = {}\n", "for charge in [-1, 0, +1]:\n", "    Li_energies = []\n", "    O_energies = []\n", "    for Ef in fermi_levels:\n", "        Eform1, _, _ = compute_formation_energy(\n", "            vac_li,\n", "            pristine_structure_energy=E_pristine,\n", "            defect_structure_energy=E_vac_Li_dict[charge],\n", "            element=\"Li\",\n", "            charge=charge,\n", "            phase_entries=phase_entries,\n", "            fermi_level=Ef\n", "        )\n", "        Li_energies.append(Eform1)\n", "        \n", "        Eform2, _, _ = compute_formation_energy(\n", "            vac_o,\n", "            pristine_structure_energy=E_pristine,\n", "            defect_structure_energy=E_vac_O_dict[charge],\n", "            element=\"O\",\n", "            charge=charge,\n", "            phase_entries=phase_entries,\n", "            fermi_level=Ef\n", "        )\n", "        O_energies.append(Eform2)       \n", "    defect_Li_energies[charge] = Li_energies\n", "    defect_O_energies[charge] = O_energies"]}, {"cell_type": "code", "execution_count": 19, "id": "cc11b150-fd6f-4613-ae1d-aa2cb7f93393", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 5))\n", "\n", "for charge, energies in defect_Li_energies.items():\n", "    plt.plot(fermi_levels, energies, label=f\"Li vac q = {charge}\")\n", "\n", "for charge, energies in defect_O_energies.items():\n", "    plt.plot(fermi_levels, energies, label=f\"O vac q = {charge}\")    \n", "\n", "plt.xlabel(\"Fermi Level (eV)\")\n", "plt.ylabel(\"Formation Energy (eV)\")\n", "plt.title(\"Defect Formation Energy vs Fermi Level\")\n", "plt.grid(True)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "3eb78d5e-ff44-40db-a861-c6ec81554d56", "metadata": {}, "source": ["# 결함 농도 계산"]}, {"cell_type": "code", "execution_count": 21, "id": "49475b79-f771-491c-9e64-55ec0f5a7487", "metadata": {}, "outputs": [], "source": ["def compute_defect_concentration_cm3(structure, element_symbol, formation_energy, temperature=300):\n", "    \"\"\"\n", "    구조 기반으로 결함 농도 [cm^-3] 계산 (온도 고정 가능)\n", "    \n", "    Parameters:\n", "        structure (Structure): pymatgen 구조 객체\n", "        element_symbol (str): 제거된 원소 기호 (예: \"Li\", \"O\")\n", "        formation_energy (float): 결함 형성 에너지 (eV)\n", "        temperature (float): 온도 (K), 기본값 300K\n", "        \n", "    Returns:\n", "        concentration (float): 결함 농도 [cm^-3]\n", "    \"\"\"\n", "    k_B = 8.617e-5  # eV/K\n", "\n", "    # 해당 원자의 자리 수 계산\n", "    element = Element(element_symbol)\n", "    n_sites = structure.composition.get_el_amt_dict().get(element_symbol, 0)\n", "\n", "    # 구조 부피 [Å³] → [cm³]\n", "    volume_cm3 = structure.volume * 1e-24\n", "\n", "    # 단위 부피당 자리 수 [sites/cm³]\n", "    site_density = n_sites / volume_cm3\n", "\n", "    # 농도 계산\n", "    exponent = -formation_energy / (k_B * temperature)\n", "    concentration_cm3 = site_density * np.exp(exponent)\n", "\n", "    return concentration_cm3"]}, {"cell_type": "code", "execution_count": 23, "id": "56c72d56-74b9-437a-901e-32444ecc539c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.607207561981311e-128 cm-3\n", "6.18487650221371e-150 cm-3\n", "1.4047570503904908e-170 cm-3\n", "0.0 cm-3\n", "0.0 cm-3\n", "0.0 cm-3\n"]}], "source": ["print(compute_defect_concentration_cm3(cubic_structure, \"Li\", 8.9574), 'cm-3')\n", "print(compute_defect_concentration_cm3(cubic_structure, \"Li\", 10.2446), 'cm-3')\n", "print(compute_defect_concentration_cm3(cubic_structure, \"Li\", 11.4734), 'cm-3')\n", "\n", "print(compute_defect_concentration_cm3(cubic_structure, \"O\", 26.6511), 'cm-3')\n", "print(compute_defect_concentration_cm3(cubic_structure, \"O\", 22.6845), 'cm-3')\n", "print(compute_defect_concentration_cm3(cubic_structure, \"O\", 21.2296), 'cm-3')"]}, {"cell_type": "markdown", "id": "be8540dd-1246-428e-b07b-cf9870d8206f", "metadata": {}, "source": ["# 표면 구조 만들기 및 에너지 계산"]}, {"cell_type": "code", "execution_count": 43, "id": "ec28a542-21a1-4968-b808-1b01cd3eb54e", "metadata": {}, "outputs": [], "source": ["from pymatgen.core.surface import SlabGenerator\n", "\n", "def generate_surface_slab(bulk_structure, miller_index, min_slab_size=10.0, min_vacuum_size=12.0, center_slab=True):\n", "    \"\"\"\n", "    주어진 벌크 구조로부터 특정 면의 표면(slab) 구조를 생성하는 함수\n", "    \n", "    Parameters:\n", "        bulk_structure (Structure): pymatgen의 벌크 구조 객체\n", "        miller_index (tuple): 밀러 지수 (예: (1, 0, 0))\n", "        min_slab_size (float): 슬랩 두께 [Å]\n", "        min_vacuum_size (float): 진공 두께 [Å]\n", "        center_slab (bool): 슬랩을 진공 중앙에 위치시킬지 여부\n", "        \n", "    Returns:\n", "        slab (Structure): 생성된 slab 구조\n", "    \"\"\"\n", "    slabgen = SlabGenerator(\n", "        initial_structure=bulk_structure,\n", "        miller_index=miller_index,\n", "        min_slab_size=min_slab_size,\n", "        min_vacuum_size=min_vacuum_size,\n", "        center_slab=center_slab,\n", "        in_unit_planes=True\n", "    )\n", "    \n", "    # 첫 번째 슬랩 구조 반환 (보통 대칭성 고려된 여러 슬랩 중 첫 번째)\n", "    slabs = slabgen.get_slabs()\n", "    print(len(slabs), 'terminations present')\n", "\n", "    return slabs[0]"]}, {"cell_type": "code", "execution_count": 47, "id": "dce072e0-ac4e-46f1-83bc-84a63b7ddf80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 terminations present\n", "슬랩 구조 생성 완료: 원자 수 = 60\n"]}], "source": ["slab_100 = generate_surface_slab(\n", "    bulk_structure=structure,\n", "    miller_index=(1, 0, 0)\n", ")\n", "\n", "# slab.to(filename=\"Li2O_100_slab.cif\")\n", "print(f\"슬랩 구조 생성 완료: 원자 수 = {len(slab_100)}\")"]}, {"cell_type": "code", "execution_count": 49, "id": "e96af8bd-d5df-425f-9440-345da5525a70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 terminations present\n", "슬랩 구조 생성 완료: 원자 수 = 60\n", "2 terminations present\n", "슬랩 구조 생성 완료: 원자 수 = 30\n", "1 terminations present\n", "슬랩 구조 생성 완료: 원자 수 = 60\n"]}], "source": ["slab_110 = generate_surface_slab(\n", "    bulk_structure=structure,\n", "    miller_index=(1, 1, 0)\n", ")\n", "\n", "# slab.to(filename=\"Li2O_100_slab.cif\")\n", "print(f\"슬랩 구조 생성 완료: 원자 수 = {len(slab_110)}\")\n", "\n", "slab_111 = generate_surface_slab(\n", "    bulk_structure=structure,\n", "    miller_index=(1, 1, 1)\n", ")\n", "\n", "# slab.to(filename=\"Li2O_100_slab.cif\")\n", "print(f\"슬랩 구조 생성 완료: 원자 수 = {len(slab_111)}\")\n", "\n", "slab_210 = generate_surface_slab(\n", "    bulk_structure=structure,\n", "    miller_index=(2, 1, 0)\n", ")\n", "\n", "# slab.to(filename=\"Li2O_100_slab.cif\")\n", "print(f\"슬랩 구조 생성 완료: 원자 수 = {len(slab_210)}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 51, "id": "e8ba0915-10f8-4d33-a08a-196334ff7151", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on cuda\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.185274514791672e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.274436532307667e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.265135297657794e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.272234062968677e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.270592582272826e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.24783132466019e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.238468647694233e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.259920959534191e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.227108398634247e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.27180632699787e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.35281296870198e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.32697577115997e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.271887118207194e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.250317172914199e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.351916603081254e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.289501884205008e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Structure 1: MLP relaxed energy = -291.3693\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.200684272230848e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.269396788885438e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.27535100696272e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.270645096334252e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.273667306922149e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.278919058418374e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.226844680684111e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.238212538204602e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.257564640685511e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.255492651943729e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.258410808833986e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.218339357402901e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.24795044001239e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Structure 2: MLP relaxed energy = -294.5130\n", "Structure 3: MLP relaxed energy = -144.0806\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.271949329498085e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.230291732415881e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.247871839236932e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.270681299149519e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.275824673659558e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.283643512803813e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.280413548631044e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.208204760218573e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.282688908424385e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.232875824658168e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.21736415833488e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.250357203727573e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.239517423405027e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.274977216131026e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.29570784176481e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.26578576042207e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.261796483982335e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.267731241575298e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.245819628312386e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.275279453665485e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.294129694407386e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.292807087075695e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.299157018289821e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.328753484269228e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.302994871798621e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.284659666517016e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.292194070677487e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.271940104051223e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.248085500263865e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.331688172805163e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Structure 4: MLP relaxed energy = -291.4432\n"]}], "source": ["# 각 구조에 대한 MLP relaxation 에너지\n", "from chgnet.model import StructOptimizer\n", "relaxer = StructOptimizer()\n", "relaxed_structures = []\n", "energies = []\n", "for i, entry in enumerate([slab_100, slab_110, slab_111, slab_210]):\n", "    result = relaxer.relax(entry, verbose=False)\n", "    relaxed_energy = result['trajectory'].energies[-1]\n", "    relaxed_structures.append(result['final_structure'])\n", "    energies.append(relaxed_energy)\n", "    print(f\"Structure {i+1}: MLP relaxed energy = {relaxed_energy:.4f}\")"]}, {"cell_type": "code", "execution_count": 54, "id": "9ad97b42-0df2-461d-8cb1-f0d37d809db7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-59.12381\n"]}], "source": ["# bulk 에너지 계산\n", "result = relaxer.relax(structure, verbose=False)\n", "relaxed_energy_Li2O = result['trajectory'].energies[-1]\n", "print(relaxed_energy_Li2O)\n"]}, {"cell_type": "markdown", "id": "7c9fdcb8-a529-418a-9241-fd35aef4eb7a", "metadata": {}, "source": ["# 표면 에너지 계산"]}, {"cell_type": "code", "execution_count": 62, "id": "f54820d4-0fd8-4bf8-bf53-011403acb2d1", "metadata": {}, "outputs": [], "source": ["def compute_surface_energy_from_slab(slab_structure, E_slab, bulk_energy_per_unit, bulk_structure, unit=\"J/m2\"):\n", "    \"\"\"\n", "    슬랩 구조로부터 표면 에너지를 자동으로 계산\n", "    \n", "    Parameters:\n", "        slab_structure (Structure): pymatgen 슬랩 구조\n", "        E_slab: 슬랩 구조 에너지\n", "        bulk_energy_per_unit (float): 단위 셀 하나의 벌크 에너지 (eV)\n", "        bulk_structure (Structure, optional): 벌크 구조 (단위 셀 수 자동 유도 시 사용)\n", "        unit (str): \"J/m2\" (기본값) 또는 \"eV/Å2\"\n", "    \n", "    Returns:\n", "        surface_energy (float): 표면 에너지\n", "    \"\"\"\n", "\n", "    # 1. 표면 면적 계산 (a, b 평면 기준)\n", "    a_vec, b_vec = slab_structure.lattice.matrix[:2]\n", "    surface_area = np.linalg.norm(np.cross(a_vec, b_vec))  # Å²\n", "\n", "    # 2. 벌크 셀 개수 유도\n", "    unit_atoms = bulk_structure.composition.num_atoms\n", "    n_bulk_units = slab_structure.composition.num_atoms / unit_atoms\n", "\n", "    # 3. 에너지 차이 및 표면 에너지 계산\n", "    delta_E = E_slab - n_bulk_units * bulk_energy_per_unit\n", "    gamma_eV_per_A2 = delta_E / (2 * surface_area)\n", "\n", "    if unit == \"J/m2\":\n", "        return gamma_eV_per_A2 * 16.0218\n", "    else:\n", "        return gamma_eV_per_A2\n"]}, {"cell_type": "code", "execution_count": 63, "id": "2a08ab17-057f-4ae3-ba99-f7b698b8deab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Surface energy of (100): 3.141 J/m²\n", "✅ Surface energy of (110): 0.579 J/m²\n", "✅ Surface energy of (111): 3.170 J/m²\n", "✅ Surface energy of (210): 1.378 J/m²\n"]}], "source": ["indices = ['(100)', '(110)', '(111)', '(210)']\n", "for ind, sstr, eslab in zip(indices, relaxed_structures, energies):\n", "    gamma = compute_surface_energy_from_slab(\n", "        slab_structure=sstr,\n", "        E_slab=eslab,\n", "        bulk_energy_per_unit=relaxed_energy_Li2O,\n", "        bulk_structure=structure\n", "    )\n", "    \n", "    print(f\"✅ Surface energy of {ind}: {gamma:.3f} J/m²\")"]}, {"cell_type": "code", "execution_count": 66, "id": "a39c58c6-f607-4408-ac0c-f7396b43bc8e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/cmss/lib/python3.13/site-packages/pymatgen/analysis/wulff.py:313: UserWarning: FigureCanvasAgg is non-interactive, and thus cannot be shown\n", "  self.get_plot(*args, **kwargs).get_figure().show()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pymatgen.analysis.wulff import WulffShape\n", "\n", "# 1. Miller indices 및 대응하는 표면 에너지 (J/m²)\n", "surface_energies = {\n", "    (1, 0, 0): 3.141,\n", "    (1, 1, 0): 0.579,\n", "    (1, 1, 1): 3.170,\n", "    (2, 1, 0): 1.378\n", "}\n", "\n", "# 2. <PERSON><PERSON><PERSON> shape 생성\n", "wulff = WulffShape(\n", "    lattice=structure.lattice,\n", "    miller_list=list(surface_energies.keys()),\n", "    e_surf_list=list(surface_energies.values())\n", ")\n", "\n", "# 3. 시각화\n", "wulff.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}
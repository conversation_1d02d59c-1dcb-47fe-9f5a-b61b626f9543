{"cells": [{"cell_type": "markdown", "id": "80dc7446-23bd-4c88-9e6e-a2613ca8d999", "metadata": {}, "source": ["# Materials Project에서 구조 가져오기 및 cubic supercell 만들기"]}, {"cell_type": "code", "execution_count": 3, "id": "e8d21ab0-fa51-4d45-b0e1-6fe95e0e8940", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Full Formula (Li8 O4)\n", "Reduced Formula: Li2O\n", "abc   :   4.653709   4.653709   4.653709\n", "angles:  90.000000  90.000000  90.000000\n", "pbc   :       True       True       True\n", "Sites (12)\n", "  #  SP       a     b     c\n", "---  ----  ----  ----  ----\n", "  0  Li    0.25  0.75  0.25\n", "  1  Li    0.75  0.25  0.75\n", "  2  Li    0.25  0.25  0.75\n", "  3  Li    0.75  0.75  0.25\n", "  4  Li    0.75  0.75  0.75\n", "  5  Li    0.25  0.25  0.25\n", "  6  Li    0.75  0.25  0.25\n", "  7  Li    0.25  0.75  0.75\n", "  8  O     0     0     0\n", "  9  O     0     0.5   0.5\n", " 10  O     0.5   0     0.5\n", " 11  O     0.5   0.5   0\n"]}], "source": ["from pymatgen.ext.matproj import MPRester  \n", "\n", "API_KEY = \"cwJruOX65TSz5C6KwWZTIj6BCHLoodVq\"\n", "\n", "with MPRester(API_KEY) as mpr:\n", "\n", "    structure = mpr.get_structure_by_material_id('mp-1960', conventional_unit_cell=True)\n", "\n", "    print(structure)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "89ef9518-37ec-40fa-b361-d3dcd153b37b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 생성된 supercell의 격자 길이:\n", "9.307418 0.000000 0.000000\n", "-0.000000 9.307418 0.000000\n", "0.000000 0.000000 9.307418\n"]}], "source": ["from pymatgen.transformations.advanced_transformations import CubicSupercellTransformation\n", "\n", "# 1. 원하는 최소 길이 (단위: Å)\n", "min_length = 9.0\n", "\n", "# 2. <PERSON><PERSON><PERSON> supercell로 변환\n", "transformer = CubicSupercellTransformation(min_length=min_length)\n", "cubic_structure = transformer.apply_transformation(structure)\n", "\n", "# 3. 결과 확인\n", "print(\"✅ 생성된 supercell의 격자 길이:\")\n", "print(cubic_structure.lattice)"]}, {"cell_type": "code", "execution_count": 5, "id": "5c0301bc-e1d6-4ed7-bec4-a154d3d3885f", "metadata": {}, "outputs": [{"ename": "PmgVaspPspDirError", "evalue": "PMG_VASP_PSP_DIR is not set. Please set PMG_VASP_PSP_DIR in .pmgrc.yaml or use potcar_spec=True argument.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mPmgVaspPspDirError\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      3\u001b[39m vasp_input_set = MPRelaxSet(cubic_structure)\n\u001b[32m      4\u001b[39m dirname = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mLi2O_perfect\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m \u001b[43mvasp_input_set\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrite_input\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdirname\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/sets.py:368\u001b[39m, in \u001b[36mVaspInputSet.write_input\u001b[39m\u001b[34m(self, output_dir, make_dir_if_not_present, include_cif, potcar_spec, zip_output)\u001b[39m\n\u001b[32m    366\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m PmgVaspPspDirError:\n\u001b[32m    367\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m potcar_spec:\n\u001b[32m--> \u001b[39m\u001b[32m368\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m PmgVaspPspDirError(\n\u001b[32m    369\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mPMG_VASP_PSP_DIR is not set. Please set PMG_VASP_PSP_DIR\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    370\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33m in .pmgrc.yaml or use potcar_spec=True argument.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    371\u001b[39m         ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    373\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m vasp_input \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    374\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mvasp_input is None\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mPmgVaspPspDirError\u001b[39m: PMG_VASP_PSP_DIR is not set. Please set PMG_VASP_PSP_DIR in .pmgrc.yaml or use potcar_spec=True argument."]}], "source": ["# perfect한 구조의 계산 input 생성\n", "from pymatgen.io.vasp.sets import MPRelaxSet\n", "vasp_input_set = MPRelaxSet(cubic_structure)\n", "dirname = f\"Li2O_perfect\"\n", "vasp_input_set.write_input(dirname)"]}, {"cell_type": "markdown", "id": "f2d03e8f-e0d8-46bd-a03b-ba9c917dbf66", "metadata": {}, "source": ["# Li vacancy 및 O vacancy를 가진 구조 생성"]}, {"cell_type": "code", "execution_count": 6, "id": "36ad7fa0-9aa2-4b98-b114-a3b647091a33", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["Structure Summary\n", "<PERSON><PERSON><PERSON>\n", "    abc : 9.307417527672138 9.307417527672138 9.307417527672138\n", " angles : 90.0 90.0 90.0\n", " volume : 806.2831613632977\n", "      A : 9.307417527672138 0.0 5.699149541795828e-16\n", "      B : -5.699149541795828e-16 9.307417527672138 5.699149541795828e-16\n", "      C : 0.0 0.0 9.307417527672138\n", "    pbc : True True True\n", "PeriodicSite: Li (1.163, 3.49, 1.163) [0.125, 0.375, 0.125]\n", "PeriodicSite: Li (1.163, 3.49, 5.817) [0.125, 0.375, 0.625]\n", "PeriodicSite: Li (1.163, 8.144, 1.163) [0.125, 0.875, 0.125]\n", "PeriodicSite: Li (1.163, 8.144, 5.817) [0.125, 0.875, 0.625]\n", "PeriodicSite: Li (5.817, 3.49, 1.163) [0.625, 0.375, 0.125]\n", "PeriodicSite: Li (5.817, 3.49, 5.817) [0.625, 0.375, 0.625]\n", "PeriodicSite: Li (5.817, 8.144, 1.163) [0.625, 0.875, 0.125]\n", "PeriodicSite: Li (5.817, 8.144, 5.817) [0.625, 0.875, 0.625]\n", "PeriodicSite: Li (3.49, 1.163, 3.49) [0.375, 0.125, 0.375]\n", "PeriodicSite: Li (3.49, 1.163, 8.144) [0.375, 0.125, 0.875]\n", "PeriodicSite: Li (3.49, 5.817, 3.49) [0.375, 0.625, 0.375]\n", "PeriodicSite: Li (3.49, 5.817, 8.144) [0.375, 0.625, 0.875]\n", "PeriodicSite: Li (8.144, 1.163, 3.49) [0.875, 0.125, 0.375]\n", "PeriodicSite: Li (8.144, 1.163, 8.144) [0.875, 0.125, 0.875]\n", "PeriodicSite: Li (8.144, 5.817, 3.49) [0.875, 0.625, 0.375]\n", "PeriodicSite: Li (8.144, 5.817, 8.144) [0.875, 0.625, 0.875]\n", "PeriodicSite: Li (1.163, 1.163, 3.49) [0.125, 0.125, 0.375]\n", "PeriodicSite: Li (1.163, 1.163, 8.144) [0.125, 0.125, 0.875]\n", "PeriodicSite: Li (1.163, 5.817, 3.49) [0.125, 0.625, 0.375]\n", "PeriodicSite: Li (1.163, 5.817, 8.144) [0.125, 0.625, 0.875]\n", "PeriodicSite: Li (5.817, 1.163, 3.49) [0.625, 0.125, 0.375]\n", "PeriodicSite: Li (5.817, 1.163, 8.144) [0.625, 0.125, 0.875]\n", "PeriodicSite: Li (5.817, 5.817, 3.49) [0.625, 0.625, 0.375]\n", "PeriodicSite: Li (5.817, 5.817, 8.144) [0.625, 0.625, 0.875]\n", "PeriodicSite: Li (3.49, 3.49, 1.163) [0.375, 0.375, 0.125]\n", "PeriodicSite: Li (3.49, 3.49, 5.817) [0.375, 0.375, 0.625]\n", "PeriodicSite: Li (3.49, 8.144, 1.163) [0.375, 0.875, 0.125]\n", "PeriodicSite: Li (3.49, 8.144, 5.817) [0.375, 0.875, 0.625]\n", "PeriodicSite: Li (8.144, 3.49, 1.163) [0.875, 0.375, 0.125]\n", "PeriodicSite: Li (8.144, 3.49, 5.817) [0.875, 0.375, 0.625]\n", "PeriodicSite: Li (8.144, 8.144, 1.163) [0.875, 0.875, 0.125]\n", "PeriodicSite: Li (8.144, 8.144, 5.817) [0.875, 0.875, 0.625]\n", "PeriodicSite: Li (3.49, 3.49, 3.49) [0.375, 0.375, 0.375]\n", "PeriodicSite: Li (3.49, 3.49, 8.144) [0.375, 0.375, 0.875]\n", "PeriodicSite: Li (3.49, 8.144, 3.49) [0.375, 0.875, 0.375]\n", "PeriodicSite: Li (3.49, 8.144, 8.144) [0.375, 0.875, 0.875]\n", "PeriodicSite: Li (8.144, 3.49, 3.49) [0.875, 0.375, 0.375]\n", "PeriodicSite: Li (8.144, 3.49, 8.144) [0.875, 0.375, 0.875]\n", "PeriodicSite: Li (8.144, 8.144, 3.49) [0.875, 0.875, 0.375]\n", "PeriodicSite: Li (8.144, 8.144, 8.144) [0.875, 0.875, 0.875]\n", "PeriodicSite: Li (1.163, 1.163, 1.163) [0.125, 0.125, 0.125]\n", "PeriodicSite: Li (1.163, 1.163, 5.817) [0.125, 0.125, 0.625]\n", "PeriodicSite: Li (1.163, 5.817, 1.163) [0.125, 0.625, 0.125]\n", "PeriodicSite: Li (1.163, 5.817, 5.817) [0.125, 0.625, 0.625]\n", "PeriodicSite: Li (5.817, 1.163, 1.163) [0.625, 0.125, 0.125]\n", "PeriodicSite: Li (5.817, 1.163, 5.817) [0.625, 0.125, 0.625]\n", "PeriodicSite: Li (5.817, 5.817, 1.163) [0.625, 0.625, 0.125]\n", "PeriodicSite: Li (5.817, 5.817, 5.817) [0.625, 0.625, 0.625]\n", "PeriodicSite: Li (3.49, 1.163, 1.163) [0.375, 0.125, 0.125]\n", "PeriodicSite: Li (3.49, 1.163, 5.817) [0.375, 0.125, 0.625]\n", "PeriodicSite: Li (3.49, 5.817, 1.163) [0.375, 0.625, 0.125]\n", "PeriodicSite: Li (3.49, 5.817, 5.817) [0.375, 0.625, 0.625]\n", "PeriodicSite: Li (8.144, 1.163, 1.163) [0.875, 0.125, 0.125]\n", "PeriodicSite: Li (8.144, 1.163, 5.817) [0.875, 0.125, 0.625]\n", "PeriodicSite: Li (8.144, 5.817, 1.163) [0.875, 0.625, 0.125]\n", "PeriodicSite: Li (8.144, 5.817, 5.817) [0.875, 0.625, 0.625]\n", "PeriodicSite: Li (1.163, 3.49, 3.49) [0.125, 0.375, 0.375]\n", "PeriodicSite: Li (1.163, 3.49, 8.144) [0.125, 0.375, 0.875]\n", "PeriodicSite: Li (1.163, 8.144, 3.49) [0.125, 0.875, 0.375]\n", "PeriodicSite: Li (1.163, 8.144, 8.144) [0.125, 0.875, 0.875]\n", "PeriodicSite: Li (5.817, 3.49, 3.49) [0.625, 0.375, 0.375]\n", "PeriodicSite: Li (5.817, 3.49, 8.144) [0.625, 0.375, 0.875]\n", "PeriodicSite: Li (5.817, 8.144, 3.49) [0.625, 0.875, 0.375]\n", "PeriodicSite: Li (5.817, 8.144, 8.144) [0.625, 0.875, 0.875]\n", "PeriodicSite: O (0.0, 0.0, 4.654) [0.0, 0.0, 0.5]\n", "PeriodicSite: O (-2.85e-16, 4.654, 2.85e-16) [1.468e-33, 0.5, 2.405e-33]\n", "PeriodicSite: O (-2.85e-16, 4.654, 4.654) [1.468e-33, 0.5, 0.5]\n", "PeriodicSite: O (4.654, 0.0, 2.85e-16) [0.5, 0.0, 2.405e-33]\n", "PeriodicSite: O (4.654, 0.0, 4.654) [0.5, 0.0, 0.5]\n", "PeriodicSite: O (4.654, 4.654, 5.699e-16) [0.5, 0.5, 4.809e-33]\n", "PeriodicSite: O (4.654, 4.654, 4.654) [0.5, 0.5, 0.5]\n", "PeriodicSite: O (-1.425e-16, 2.327, 2.327) [7.339e-34, 0.25, 0.25]\n", "PeriodicSite: O (-1.425e-16, 2.327, 6.981) [7.339e-34, 0.25, 0.75]\n", "PeriodicSite: O (-4.274e-16, 6.981, 2.327) [2.042e-33, 0.75, 0.25]\n", "PeriodicSite: O (-4.274e-16, 6.981, 6.981) [2.042e-33, 0.75, 0.75]\n", "PeriodicSite: O (4.654, 2.327, 2.327) [0.5, 0.25, 0.25]\n", "PeriodicSite: O (4.654, 2.327, 6.981) [0.5, 0.25, 0.75]\n", "PeriodicSite: O (4.654, 6.981, 2.327) [0.5, 0.75, 0.25]\n", "PeriodicSite: O (4.654, 6.981, 6.981) [0.5, 0.75, 0.75]\n", "PeriodicSite: O (2.327, 0.0, 2.327) [0.25, 0.0, 0.25]\n", "PeriodicSite: O (2.327, 0.0, 6.981) [0.25, 0.0, 0.75]\n", "PeriodicSite: O (2.327, 4.654, 2.327) [0.25, 0.5, 0.25]\n", "PeriodicSite: O (2.327, 4.654, 6.981) [0.25, 0.5, 0.75]\n", "PeriodicSite: O (6.981, 0.0, 2.327) [0.75, 0.0, 0.25]\n", "PeriodicSite: O (6.981, 0.0, 6.981) [0.75, 0.0, 0.75]\n", "PeriodicSite: O (6.981, 4.654, 2.327) [0.75, 0.5, 0.25]\n", "PeriodicSite: O (6.981, 4.654, 6.981) [0.75, 0.5, 0.75]\n", "PeriodicSite: O (2.327, 2.327, 2.85e-16) [0.25, 0.25, 2.405e-33]\n", "PeriodicSite: O (2.327, 2.327, 4.654) [0.25, 0.25, 0.5]\n", "PeriodicSite: O (2.327, 6.981, 5.699e-16) [0.25, 0.75, 4.809e-33]\n", "PeriodicSite: O (2.327, 6.981, 4.654) [0.25, 0.75, 0.5]\n", "PeriodicSite: O (6.981, 2.327, 5.699e-16) [0.75, 0.25, 4.809e-33]\n", "PeriodicSite: O (6.981, 2.327, 4.654) [0.75, 0.25, 0.5]\n", "PeriodicSite: O (6.981, 6.981, 8.549e-16) [0.75, 0.75, 1.051e-33]\n", "PeriodicSite: O (6.981, 6.981, 4.654) [0.75, 0.75, 0.5]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Li와 O 원자의 인덱스 각각 찾기\n", "li_indices = [i for i, site in enumerate(cubic_structure) if site.species_string == \"Li\"]\n", "o_indices = [i for i, site in enumerate(cubic_structure) if site.species_string == \"O\"]\n", "\n", "# 첫 번째 Li vacancy 구조\n", "vac_li = cubic_structure.copy()\n", "vac_li.remove_sites([li_indices[0]])\n", "\n", "# 첫 번째 O vacancy 구조\n", "vac_o = cubic_structure.copy()\n", "vac_o.remove_sites([o_indices[0]])"]}, {"cell_type": "code", "execution_count": 8, "id": "2634e59d-f125-483a-b2be-b9461da3053c", "metadata": {}, "outputs": [{"ename": "PmgVaspPspDirError", "evalue": "Set PMG_VASP_PSP_DIR=<directory-path> in .pmgrc.yaml (needed to find POTCARs)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mPmgVaspPspDirError\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# 1. 기준 전자수 계산 (POTCAR 기준 ZVAL)\u001b[39;00m\n\u001b[32m      4\u001b[39m base_input = MPRelaxSet(vac_li)\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m zvals = [p.nelectrons \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m \u001b[43mbase_input\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpotcar\u001b[49m]\n\u001b[32m      6\u001b[39m atom_counts = base_input.structure.composition.get_el_amt_dict()\n\u001b[32m      7\u001b[39m elements = [\u001b[38;5;28mstr\u001b[39m(p.element) \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m base_input.potcar]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/sets.py:970\u001b[39m, in \u001b[36mVaspInputSet.potcar\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    967\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mNo structure is associated with the input set!\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    969\u001b[39m user_potcar_functional = \u001b[38;5;28mself\u001b[39m.user_potcar_functional\n\u001b[32m--> \u001b[39m\u001b[32m970\u001b[39m potcar = \u001b[43mPotcar\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpotcar_symbols\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctional\u001b[49m\u001b[43m=\u001b[49m\u001b[43muser_potcar_functional\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    972\u001b[39m \u001b[38;5;66;03m# Warn if the selected POTCARs do not correspond to the chosen user_potcar_functional\u001b[39;00m\n\u001b[32m    973\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m p_single \u001b[38;5;129;01min\u001b[39;00m potcar:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/inputs.py:2898\u001b[39m, in \u001b[36mPotcar.__init__\u001b[39m\u001b[34m(self, symbols, functional, sym_potcar_map)\u001b[39m\n\u001b[32m   2896\u001b[39m \u001b[38;5;28mself\u001b[39m.functional = functional\n\u001b[32m   2897\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m symbols \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m2898\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mset_symbols\u001b[49m\u001b[43m(\u001b[49m\u001b[43msymbols\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctional\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msym_potcar_map\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/inputs.py:3028\u001b[39m, in \u001b[36mPotcar.set_symbols\u001b[39m\u001b[34m(self, symbols, functional, sym_potcar_map)\u001b[39m\n\u001b[32m   3025\u001b[39m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m[:]\n\u001b[32m   3027\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m sym_potcar_map \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3028\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mextend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mPotcarSingle\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_symbol_and_functional\u001b[49m\u001b[43m(\u001b[49m\u001b[43mel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctional\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mel\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msymbols\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   3029\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   3030\u001b[39m     \u001b[38;5;28mself\u001b[39m.extend(PotcarSingle(sym_potcar_map[el]) \u001b[38;5;28;01mfor\u001b[39;00m el \u001b[38;5;129;01min\u001b[39;00m symbols)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/inputs.py:3028\u001b[39m, in \u001b[36m<genexpr>\u001b[39m\u001b[34m(.0)\u001b[39m\n\u001b[32m   3025\u001b[39m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m[:]\n\u001b[32m   3027\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m sym_potcar_map \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3028\u001b[39m     \u001b[38;5;28mself\u001b[39m.extend(\u001b[43mPotcarSingle\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_symbol_and_functional\u001b[49m\u001b[43m(\u001b[49m\u001b[43mel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctional\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m el \u001b[38;5;129;01min\u001b[39;00m symbols)\n\u001b[32m   3029\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   3030\u001b[39m     \u001b[38;5;28mself\u001b[39m.extend(PotcarSingle(sym_potcar_map[el]) \u001b[38;5;28;01mfor\u001b[39;00m el \u001b[38;5;129;01min\u001b[39;00m symbols)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/inputs.py:2539\u001b[39m, in \u001b[36mPotcarSingle.from_symbol_and_functional\u001b[39m\u001b[34m(cls, symbol, functional)\u001b[39m\n\u001b[32m   2537\u001b[39m PMG_VASP_PSP_DIR = SETTINGS.get(\u001b[33m\"\u001b[39m\u001b[33mPMG_VASP_PSP_DIR\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   2538\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m PMG_VASP_PSP_DIR \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m2539\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m PmgVaspPspDirError(\u001b[33m\"\u001b[39m\u001b[33mSet PMG_VASP_PSP_DIR=<directory-path> in .pmgrc.yaml (needed to find POTCARs)\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   2540\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m os.path.isdir(PMG_VASP_PSP_DIR):\n\u001b[32m   2541\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mPMG_VASP_PSP_DIR\u001b[38;5;132;01m=}\u001b[39;00m\u001b[33m does not exist.\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mPmgVaspPspDirError\u001b[39m: Set PMG_VASP_PSP_DIR=<directory-path> in .pmgrc.yaml (needed to find POTCARs)"]}], "source": ["from pymatgen.io.vasp.sets import MPRelaxSet\n", "\n", "# 1. 기준 전자수 계산 (POTCAR 기준 ZVAL)\n", "base_input = MPRelaxSet(vac_li)\n", "zvals = [p.nelectrons for p in base_input.potcar]\n", "atom_counts = base_input.structure.composition.get_el_amt_dict()\n", "elements = [str(p.element) for p in base_input.potcar]\n", "\n", "base_nelect = sum(zval * atom_counts[el] for zval, el in zip(zvals, elements))\n", "\n", "# 2. 전하 상태별 input 생성\n", "for charge in [0, +1, -1]:\n", "    nelect = base_nelect - charge\n", "    vasp_input_set = MPRelaxSet(\n", "        vac_li,\n", "        user_incar_settings={\"NELECT\": nelect}\n", "    )\n", "    dirname = f\"Li2O_vac_Li_charge_{charge:+d}\"\n", "    vasp_input_set.write_input(dirname)\n", "    print(f\"✅ {dirname}/ (NELECT={nelect}) 생성 완료\")"]}, {"cell_type": "code", "execution_count": 9, "id": "0995a520-04b8-4eaf-a54d-a7ebaeb89d2d", "metadata": {}, "outputs": [{"ename": "PmgVaspPspDirError", "evalue": "Set PMG_VASP_PSP_DIR=<directory-path> in .pmgrc.yaml (needed to find POTCARs)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mPmgVaspPspDirError\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 1. 기준 전자수 계산 (POTCAR 기준 ZVAL)\u001b[39;00m\n\u001b[32m      2\u001b[39m base_input = MPRelaxSet(vac_o)\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m zvals = [p.nelectrons \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m \u001b[43mbase_input\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpotcar\u001b[49m]\n\u001b[32m      4\u001b[39m atom_counts = base_input.structure.composition.get_el_amt_dict()\n\u001b[32m      5\u001b[39m elements = [\u001b[38;5;28mstr\u001b[39m(p.element) \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m base_input.potcar]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/sets.py:970\u001b[39m, in \u001b[36mVaspInputSet.potcar\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    967\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mNo structure is associated with the input set!\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    969\u001b[39m user_potcar_functional = \u001b[38;5;28mself\u001b[39m.user_potcar_functional\n\u001b[32m--> \u001b[39m\u001b[32m970\u001b[39m potcar = \u001b[43mPotcar\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpotcar_symbols\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctional\u001b[49m\u001b[43m=\u001b[49m\u001b[43muser_potcar_functional\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    972\u001b[39m \u001b[38;5;66;03m# Warn if the selected POTCARs do not correspond to the chosen user_potcar_functional\u001b[39;00m\n\u001b[32m    973\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m p_single \u001b[38;5;129;01min\u001b[39;00m potcar:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/inputs.py:2898\u001b[39m, in \u001b[36mPotcar.__init__\u001b[39m\u001b[34m(self, symbols, functional, sym_potcar_map)\u001b[39m\n\u001b[32m   2896\u001b[39m \u001b[38;5;28mself\u001b[39m.functional = functional\n\u001b[32m   2897\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m symbols \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m2898\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mset_symbols\u001b[49m\u001b[43m(\u001b[49m\u001b[43msymbols\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctional\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msym_potcar_map\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/inputs.py:3028\u001b[39m, in \u001b[36mPotcar.set_symbols\u001b[39m\u001b[34m(self, symbols, functional, sym_potcar_map)\u001b[39m\n\u001b[32m   3025\u001b[39m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m[:]\n\u001b[32m   3027\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m sym_potcar_map \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3028\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mextend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mPotcarSingle\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_symbol_and_functional\u001b[49m\u001b[43m(\u001b[49m\u001b[43mel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctional\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mel\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msymbols\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   3029\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   3030\u001b[39m     \u001b[38;5;28mself\u001b[39m.extend(PotcarSingle(sym_potcar_map[el]) \u001b[38;5;28;01mfor\u001b[39;00m el \u001b[38;5;129;01min\u001b[39;00m symbols)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/inputs.py:3028\u001b[39m, in \u001b[36m<genexpr>\u001b[39m\u001b[34m(.0)\u001b[39m\n\u001b[32m   3025\u001b[39m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m[:]\n\u001b[32m   3027\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m sym_potcar_map \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3028\u001b[39m     \u001b[38;5;28mself\u001b[39m.extend(\u001b[43mPotcarSingle\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_symbol_and_functional\u001b[49m\u001b[43m(\u001b[49m\u001b[43mel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctional\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m el \u001b[38;5;129;01min\u001b[39;00m symbols)\n\u001b[32m   3029\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   3030\u001b[39m     \u001b[38;5;28mself\u001b[39m.extend(PotcarSingle(sym_potcar_map[el]) \u001b[38;5;28;01mfor\u001b[39;00m el \u001b[38;5;129;01min\u001b[39;00m symbols)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymatgen/io/vasp/inputs.py:2539\u001b[39m, in \u001b[36mPotcarSingle.from_symbol_and_functional\u001b[39m\u001b[34m(cls, symbol, functional)\u001b[39m\n\u001b[32m   2537\u001b[39m PMG_VASP_PSP_DIR = SETTINGS.get(\u001b[33m\"\u001b[39m\u001b[33mPMG_VASP_PSP_DIR\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   2538\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m PMG_VASP_PSP_DIR \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m2539\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m PmgVaspPspDirError(\u001b[33m\"\u001b[39m\u001b[33mSet PMG_VASP_PSP_DIR=<directory-path> in .pmgrc.yaml (needed to find POTCARs)\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   2540\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m os.path.isdir(PMG_VASP_PSP_DIR):\n\u001b[32m   2541\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mPMG_VASP_PSP_DIR\u001b[38;5;132;01m=}\u001b[39;00m\u001b[33m does not exist.\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mPmgVaspPspDirError\u001b[39m: Set PMG_VASP_PSP_DIR=<directory-path> in .pmgrc.yaml (needed to find POTCARs)"]}], "source": ["# 1. 기준 전자수 계산 (POTCAR 기준 ZVAL)\n", "base_input = MPRelaxSet(vac_o)\n", "zvals = [p.nelectrons for p in base_input.potcar]\n", "atom_counts = base_input.structure.composition.get_el_amt_dict()\n", "elements = [str(p.element) for p in base_input.potcar]\n", "\n", "base_nelect = sum(zval * atom_counts[el] for zval, el in zip(zvals, elements))\n", "\n", "# 2. 전하 상태별 input 생성\n", "for charge in [0, +1, -1]:\n", "    nelect = base_nelect - charge\n", "    vasp_input_set = MPRelaxSet(\n", "        vac_o,\n", "        user_incar_settings={\"NELECT\": nelect}\n", "    )\n", "    dirname = f\"Li2O_vac_O_charge_{charge:+d}\"\n", "    vasp_input_set.write_input(dirname)\n", "    print(f\"✅ {dirname}/ (NELECT={nelect}) 생성 완료\")"]}, {"cell_type": "markdown", "id": "b9da83d4-c6e6-45d2-b4f7-49dae3df065a", "metadata": {}, "source": ["# 계산결과 받아오기"]}, {"cell_type": "code", "execution_count": 20, "id": "9319eb19-703c-4d21-bbd8-256c0984d0a0", "metadata": {}, "outputs": [], "source": ["# from pymatgen.io.vasp.outputs import Oszicar\n", "\n", "# E_pristine = Oszicar('./Li2O_perfect/OSZICAR').final_energy\n", "# E_vac_Li_n1 = Oszicar('./Li2O_vac_Li_charge_-1/OSZICAR').final_energy\n", "# E_vac_Li_0 = Oszicar('./Li2O_vac_Li_charge_+0/OSZICAR').final_energy\n", "# E_vac_Li_p1 = Oszicar('./Li2O_vac_Li_charge_+1/OSZICAR').final_energy\n", "# E_vac_O_n1 = Oszicar('./Li2O_vac_O_charge_-1/OSZICAR').final_energy\n", "# E_vac_O_0 = Oszicar('./Li2O_vac_O_charge_+0/OSZICAR').final_energy\n", "# E_vac_O_p1 = Oszicar('./Li2O_vac_O_charge_+1/OSZICAR').final_energy"]}, {"cell_type": "code", "execution_count": 10, "id": "0e61aa13-ff3e-40bf-b674-51f40de382b9", "metadata": {}, "outputs": [], "source": ["# 직접 계산 불가능한 경우: 미리 계산한 결과 에너지\n", "E_pristine = -456.43325 \n", "E_vac_Li_n1 = -451.26919 \n", "E_vac_Li_0 = -450.98199 \n", "E_vac_Li_p1 = -450.75324 \n", "E_vac_O_n1 = -439.9149 \n", "E_vac_O_0 = -444.88152 \n", "E_vac_O_p1 = -447.33636 "]}, {"cell_type": "code", "execution_count": 11, "id": "7299a33a-e986-400d-9cea-1c4a26a76d8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E_pristine = -456.43325\n", "E_vac_Li_n1 = -451.26919\n", "E_vac_Li_0 = -450.98199\n", "E_vac_Li_p1 = -450.75324\n", "E_vac_O_n1 = -439.9149\n", "E_vac_O_0 = -444.88152\n", "E_vac_O_p1 = -447.33636\n"]}], "source": ["print(f'E_pristine = {E_pristine}')\n", "print(f'E_vac_Li_n1 = {E_vac_Li_n1}')\n", "print(f'E_vac_Li_0 = {E_vac_Li_0}')\n", "print(f'E_vac_Li_p1 = {E_vac_Li_p1}')\n", "print(f'E_vac_O_n1 = {E_vac_O_n1}')\n", "print(f'E_vac_O_0 = {E_vac_O_0}')\n", "print(f'E_vac_O_p1 = {E_vac_O_p1}')"]}, {"cell_type": "code", "execution_count": 12, "id": "ec9e3aa6-b8cf-4af9-a77e-d18996648fb1", "metadata": {}, "outputs": [], "source": ["from pymatgen.entries.computed_entries import ComputedEntry\n", "from pymatgen.analysis.phase_diagram import PhaseDiagram, PDEntry\n", "from pymatgen.core import Element\n", "\n", "def compute_formation_energy(\n", "    defect_structure,\n", "    pristine_structure_energy,\n", "    defect_structure_energy,\n", "    element,\n", "    charge,\n", "    phase_entries,\n", "    fermi_level=0.0,\n", "    vbm=0.0\n", "):\n", "    \"\"\"\n", "    결함 형성 에너지 계산 함수 (pymatgen만 사용)\n", "    \n", "    Parameters:\n", "        defect_structure: pymatgen Structure with a defect\n", "        pristine_structure_energy: float\n", "        defect_structure_energy: float\n", "        element: str, removed element (e.g., 'Li' or 'O')\n", "        charge: int\n", "        phase_entries: list of PDEntry or ComputedEntry from Materials Project\n", "        fermi_level: float (default 0.0)\n", "        vbm: float, valence band maximum (default 0.0)\n", "        \n", "    Returns:\n", "        formation_energy: float\n", "    \"\"\"\n", "    # Step 1. Phase diagram으로부터 화학 퍼텐셜 (최소 안정한 영역) 구하기\n", "    pd = PhaseDiagram(phase_entries)\n", "    comp = defect_structure.composition.reduced_formula\n", "    chempots = pd.get_composition_chempots(defect_structure.composition)\n", "    mu_i = chempots[Element(element)]\n", "    \n", "    # Step 2. 결함 항 계산: +mu_i (원자 제거 시), -mu_i (원자 삽입 시)\n", "    n_removed = 1  # 이 예에서는 단일 원자 vacancy 기준\n", "    delta_mu = - n_removed * mu_i\n", "    \n", "    # Step 3. 총 형성 에너지 계산\n", "    E_form = (\n", "        defect_structure_energy\n", "        - pristine_structure_energy\n", "        + delta_mu\n", "        + charge * (fermi_level + vbm)\n", "    )\n", "    return E_form, element, charge"]}, {"cell_type": "code", "execution_count": 13, "id": "e43fea37-fe9e-4db8-b30f-1363830df75b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔋 Formation energy of Li vacancy (q=-1.0): 8.9574 eV\n", "🔋 Formation energy of Li vacancy (q=0): 10.2446 eV\n", "🔋 Formation energy of Li vacancy (q=1.0): 11.4734 eV\n", "🔋 Formation energy of O vacancy (q=-1.0): 26.6511 eV\n", "🔋 Formation energy of O vacancy (q=0): 22.6845 eV\n", "🔋 Formation energy of O vacancy (q=1.0): 21.2296 eV\n"]}], "source": ["from pymatgen.ext.matproj import MPRester\n", "\n", "# MPRester로 phase entries 가져오기\n", "\n", "with MPRester(API_KEY) as mpr:\n", "    phase_entries = mpr.get_entries_in_chemsys([\"Li\", \"O\"])\n", "\n", "# 함수 호출\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_li,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_Li_n1,\n", "    element=\"Li\",\n", "    charge=-1.0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_li,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_Li_0,\n", "    element=\"Li\",\n", "    charge=0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_li,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_Li_p1,\n", "    element=\"Li\",\n", "    charge=1.0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_o,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_O_n1,\n", "    element=\"O\",\n", "    charge=-1.0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_o,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_O_0,\n", "    element=\"O\",\n", "    charge=0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")\n", "\n", "Ef, element, charge = compute_formation_energy(\n", "    vac_o,\n", "    pristine_structure_energy=E_pristine,\n", "    defect_structure_energy=E_vac_O_p1,\n", "    element=\"O\",\n", "    charge=1.0,\n", "    phase_entries=phase_entries,\n", "    fermi_level=1.0,   # optional\n", "    vbm=0.0            # optional\n", ")\n", "\n", "print(f\"🔋 Formation energy of {element} vacancy (q={charge}): {Ef:.4f} eV\")"]}, {"cell_type": "markdown", "id": "ac272c2e-29f5-4fe4-9b9a-472797cd1616", "metadata": {}, "source": ["# Fermi level에 따른 결함생성에너지 그래프 그리기"]}, {"cell_type": "code", "execution_count": 14, "id": "b8b5b76b-acb8-4ec1-85a9-20a4bf2bb05c", "metadata": {}, "outputs": [], "source": ["E_vac_Li_dict = {-1:E_vac_Li_n1, 0:E_vac_Li_0, +1:E_vac_Li_p1}\n", "E_vac_O_dict = {-1:E_vac_O_n1, 0:E_vac_O_0, +1:E_vac_O_p1}"]}, {"cell_type": "code", "execution_count": 15, "id": "b64ef688-8083-4bb8-a3e6-5c7ab9d94288", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 예시 범위: 0 ~ 5 eV\n", "fermi_levels = np.linspace(0, 5.0, 100)\n", "\n", "# 각 charge state에 대한 형성 에너지 계산 함수 호출 (미리 정의된 함수 활용)\n", "defect_Li_energies = {}\n", "defect_O_energies = {}\n", "for charge in [-1, 0, +1]:\n", "    Li_energies = []\n", "    O_energies = []\n", "    for Ef in fermi_levels:\n", "        Eform1, _, _ = compute_formation_energy(\n", "            vac_li,\n", "            pristine_structure_energy=E_pristine,\n", "            defect_structure_energy=E_vac_Li_dict[charge],\n", "            element=\"Li\",\n", "            charge=charge,\n", "            phase_entries=phase_entries,\n", "            fermi_level=Ef\n", "        )\n", "        Li_energies.append(Eform1)\n", "        \n", "        Eform2, _, _ = compute_formation_energy(\n", "            vac_o,\n", "            pristine_structure_energy=E_pristine,\n", "            defect_structure_energy=E_vac_O_dict[charge],\n", "            element=\"O\",\n", "            charge=charge,\n", "            phase_entries=phase_entries,\n", "            fermi_level=Ef\n", "        )\n", "        O_energies.append(Eform2)       \n", "    defect_Li_energies[charge] = Li_energies\n", "    defect_O_energies[charge] = O_energies"]}, {"cell_type": "code", "execution_count": 16, "id": "cc11b150-fd6f-4613-ae1d-aa2cb7f93393", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 5))\n", "\n", "for charge, energies in defect_Li_energies.items():\n", "    plt.plot(fermi_levels, energies, label=f\"Li vac q = {charge}\")\n", "\n", "for charge, energies in defect_O_energies.items():\n", "    plt.plot(fermi_levels, energies, label=f\"O vac q = {charge}\")    \n", "\n", "plt.xlabel(\"Fermi Level (eV)\")\n", "plt.ylabel(\"Formation Energy (eV)\")\n", "plt.title(\"Defect Formation Energy vs Fermi Level\")\n", "plt.grid(True)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "3eb78d5e-ff44-40db-a861-c6ec81554d56", "metadata": {}, "source": ["# 결함 농도 계산"]}, {"cell_type": "code", "execution_count": 17, "id": "49475b79-f771-491c-9e64-55ec0f5a7487", "metadata": {}, "outputs": [], "source": ["def compute_defect_concentration_cm3(structure, element_symbol, formation_energy, temperature=300):\n", "    \"\"\"\n", "    구조 기반으로 결함 농도 [cm^-3] 계산 (온도 고정 가능)\n", "    \n", "    Parameters:\n", "        structure (Structure): pymatgen 구조 객체\n", "        element_symbol (str): 제거된 원소 기호 (예: \"Li\", \"O\")\n", "        formation_energy (float): 결함 형성 에너지 (eV)\n", "        temperature (float): 온도 (K), 기본값 300K\n", "        \n", "    Returns:\n", "        concentration (float): 결함 농도 [cm^-3]\n", "    \"\"\"\n", "    k_B = 8.617e-5  # eV/K\n", "\n", "    # 해당 원자의 자리 수 계산\n", "    element = Element(element_symbol)\n", "    n_sites = structure.composition.get_el_amt_dict().get(element_symbol, 0)\n", "\n", "    # 구조 부피 [Å³] → [cm³]\n", "    volume_cm3 = structure.volume * 1e-24\n", "\n", "    # 단위 부피당 자리 수 [sites/cm³]\n", "    site_density = n_sites / volume_cm3\n", "\n", "    # 농도 계산\n", "    exponent = -formation_energy / (k_B * temperature)\n", "    concentration_cm3 = site_density * np.exp(exponent)\n", "\n", "    return concentration_cm3"]}, {"cell_type": "code", "execution_count": 18, "id": "56c72d56-74b9-437a-901e-32444ecc539c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.607207561981311e-128 cm-3\n", "6.18487650221371e-150 cm-3\n", "1.4047570503904908e-170 cm-3\n", "0.0 cm-3\n", "0.0 cm-3\n", "0.0 cm-3\n"]}], "source": ["print(compute_defect_concentration_cm3(cubic_structure, \"Li\", 8.9574), 'cm-3')\n", "print(compute_defect_concentration_cm3(cubic_structure, \"Li\", 10.2446), 'cm-3')\n", "print(compute_defect_concentration_cm3(cubic_structure, \"Li\", 11.4734), 'cm-3')\n", "\n", "print(compute_defect_concentration_cm3(cubic_structure, \"O\", 26.6511), 'cm-3')\n", "print(compute_defect_concentration_cm3(cubic_structure, \"O\", 22.6845), 'cm-3')\n", "print(compute_defect_concentration_cm3(cubic_structure, \"O\", 21.2296), 'cm-3')"]}, {"cell_type": "markdown", "id": "be8540dd-1246-428e-b07b-cf9870d8206f", "metadata": {}, "source": ["# 표면 구조 만들기 및 에너지 계산"]}, {"cell_type": "code", "execution_count": 19, "id": "ec28a542-21a1-4968-b808-1b01cd3eb54e", "metadata": {}, "outputs": [], "source": ["from pymatgen.core.surface import SlabGenerator\n", "\n", "def generate_surface_slab(bulk_structure, miller_index, min_slab_size=10.0, min_vacuum_size=12.0, center_slab=True):\n", "    \"\"\"\n", "    주어진 벌크 구조로부터 특정 면의 표면(slab) 구조를 생성하는 함수\n", "    \n", "    Parameters:\n", "        bulk_structure (Structure): pymatgen의 벌크 구조 객체\n", "        miller_index (tuple): 밀러 지수 (예: (1, 0, 0))\n", "        min_slab_size (float): 슬랩 두께 [Å]\n", "        min_vacuum_size (float): 진공 두께 [Å]\n", "        center_slab (bool): 슬랩을 진공 중앙에 위치시킬지 여부\n", "        \n", "    Returns:\n", "        slab (Structure): 생성된 slab 구조\n", "    \"\"\"\n", "    slabgen = SlabGenerator(\n", "        initial_structure=bulk_structure,\n", "        miller_index=miller_index,\n", "        min_slab_size=min_slab_size,\n", "        min_vacuum_size=min_vacuum_size,\n", "        center_slab=center_slab,\n", "        in_unit_planes=True\n", "    )\n", "    \n", "    # 첫 번째 슬랩 구조 반환 (보통 대칭성 고려된 여러 슬랩 중 첫 번째)\n", "    slabs = slabgen.get_slabs()\n", "    print(len(slabs), 'terminations present')\n", "\n", "    return slabs[0]"]}, {"cell_type": "code", "execution_count": 20, "id": "dce072e0-ac4e-46f1-83bc-84a63b7ddf80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 terminations present\n", "슬랩 구조 생성 완료: 원자 수 = 60\n"]}], "source": ["slab_100 = generate_surface_slab(\n", "    bulk_structure=structure,\n", "    miller_index=(1, 0, 0)\n", ")\n", "\n", "# slab.to(filename=\"Li2O_100_slab.cif\")\n", "print(f\"슬랩 구조 생성 완료: 원자 수 = {len(slab_100)}\")"]}, {"cell_type": "code", "execution_count": 22, "id": "e96af8bd-d5df-425f-9440-345da5525a70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 terminations present\n", "슬랩 구조 생성 완료: 원자 수 = 60\n", "2 terminations present\n", "슬랩 구조 생성 완료: 원자 수 = 30\n", "1 terminations present\n", "슬랩 구조 생성 완료: 원자 수 = 60\n"]}], "source": ["slab_110 = generate_surface_slab(\n", "    bulk_structure=structure,\n", "    miller_index=(1, 1, 0)\n", ")\n", "\n", "# slab.to(filename=\"Li2O_100_slab.cif\")\n", "print(f\"슬랩 구조 생성 완료: 원자 수 = {len(slab_110)}\")\n", "\n", "slab_111 = generate_surface_slab(\n", "    bulk_structure=structure,\n", "    miller_index=(1, 1, 1)\n", ")\n", "\n", "# slab.to(filename=\"Li2O_100_slab.cif\")\n", "print(f\"슬랩 구조 생성 완료: 원자 수 = {len(slab_111)}\")\n", "\n", "slab_210 = generate_surface_slab(\n", "    bulk_structure=structure,\n", "    miller_index=(2, 1, 0)\n", ")\n", "\n", "# slab.to(filename=\"Li2O_100_slab.cif\")\n", "print(f\"슬랩 구조 생성 완료: 원자 수 = {len(slab_210)}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "id": "e8ba0915-10f8-4d33-a08a-196334ff7151", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on mps\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.291729954169937e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.257913880455431e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.257030743850184e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.254709595835307e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.222294934794481e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.216246046242773e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.30856078934569e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.346059477242686e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.30968538821827e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.266185306623694e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.271749304715235e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.270485880451037e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.348568778477513e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.249677042502319e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.244682428922033e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Structure 1: MLP relaxed energy = -291.3693\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.227907078937279e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.2658175050125e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.278697326050133e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.238725894352385e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.230380321113537e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.229959541371082e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.277734848444792e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.274175754726691e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.288374141010565e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.220852998020662e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.2327665147952e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.239652449172027e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.24290045561797e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.244397767990987e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Structure 2: MLP relaxed energy = -294.5130\n", "Structure 3: MLP relaxed energy = -144.0806\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.22790707893728e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.202992573908319e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.294901514117996e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.258410280706696e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.184328051203419e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.297748560743682e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.240831822292045e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.310010194100099e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.241523089910219e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.277953492379726e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.236427295719056e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.314436477311353e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.269297670021927e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.224322351031006e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.247664478751963e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.276299293733901e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.274861382163062e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.292550981067777e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.252959246937808e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.266718679687005e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.248374818755963e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.245376649827431e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.270456063093668e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.316317215933268e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.312204631754727e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.292124457020769e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.269853203780896e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.272106251204285e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.258942436067592e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.290621617121872e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Structure 4: MLP relaxed energy = -291.4432\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/ase/filters.py:606: RuntimeWarning: logm result may be inaccurate, approximate err = 6.259616320336322e-13\n", "  pos[natoms:] = self.logm(pos[natoms:]) * self.exp_cell_factor\n"]}], "source": ["# 각 구조에 대한 MLP relaxation 에너지\n", "from chgnet.model import StructOptimizer\n", "relaxer = StructOptimizer()\n", "relaxed_structures = []\n", "energies = []\n", "for i, entry in enumerate([slab_100, slab_110, slab_111, slab_210]):\n", "    result = relaxer.relax(entry, verbose=False)\n", "    relaxed_energy = result['trajectory'].energies[-1]\n", "    relaxed_structures.append(result['final_structure'])\n", "    energies.append(relaxed_energy)\n", "    print(f\"Structure {i+1}: MLP relaxed energy = {relaxed_energy:.4f}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "9ad97b42-0df2-461d-8cb1-f0d37d809db7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-59.12380599975586\n"]}], "source": ["# bulk 에너지 계산\n", "result = relaxer.relax(structure, verbose=False)\n", "relaxed_energy_Li2O = result['trajectory'].energies[-1]\n", "print(relaxed_energy_Li2O)\n"]}, {"cell_type": "markdown", "id": "7c9fdcb8-a529-418a-9241-fd35aef4eb7a", "metadata": {}, "source": ["# 표면 에너지 계산"]}, {"cell_type": "code", "execution_count": 26, "id": "f54820d4-0fd8-4bf8-bf53-011403acb2d1", "metadata": {}, "outputs": [], "source": ["def compute_surface_energy_from_slab(slab_structure, E_slab, bulk_energy_per_unit, bulk_structure, unit=\"J/m2\"):\n", "    \"\"\"\n", "    슬랩 구조로부터 표면 에너지를 자동으로 계산\n", "    \n", "    Parameters:\n", "        slab_structure (Structure): pymatgen 슬랩 구조\n", "        E_slab: 슬랩 구조 에너지\n", "        bulk_energy_per_unit (float): 단위 셀 하나의 벌크 에너지 (eV)\n", "        bulk_structure (Structure, optional): 벌크 구조 (단위 셀 수 자동 유도 시 사용)\n", "        unit (str): \"J/m2\" (기본값) 또는 \"eV/Å2\"\n", "    \n", "    Returns:\n", "        surface_energy (float): 표면 에너지\n", "    \"\"\"\n", "\n", "    # 1. 표면 면적 계산 (a, b 평면 기준)\n", "    a_vec, b_vec = slab_structure.lattice.matrix[:2]\n", "    surface_area = np.linalg.norm(np.cross(a_vec, b_vec))  # Å²\n", "\n", "    # 2. 벌크 셀 개수 유도\n", "    unit_atoms = bulk_structure.composition.num_atoms\n", "    n_bulk_units = slab_structure.composition.num_atoms / unit_atoms\n", "\n", "    # 3. 에너지 차이 및 표면 에너지 계산\n", "    delta_E = E_slab - n_bulk_units * bulk_energy_per_unit\n", "    gamma_eV_per_A2 = delta_E / (2 * surface_area)\n", "\n", "    if unit == \"J/m2\":\n", "        return gamma_eV_per_A2 * 16.0218\n", "    else:\n", "        return gamma_eV_per_A2\n"]}, {"cell_type": "code", "execution_count": 27, "id": "2a08ab17-057f-4ae3-ba99-f7b698b8deab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Surface energy of (100): 3.141 J/m²\n", "✅ Surface energy of (110): 0.579 J/m²\n", "✅ Surface energy of (111): 3.170 J/m²\n", "✅ Surface energy of (210): 1.378 J/m²\n"]}], "source": ["indices = ['(100)', '(110)', '(111)', '(210)']\n", "for ind, sstr, eslab in zip(indices, relaxed_structures, energies):\n", "    gamma = compute_surface_energy_from_slab(\n", "        slab_structure=sstr,\n", "        E_slab=eslab,\n", "        bulk_energy_per_unit=relaxed_energy_Li2O,\n", "        bulk_structure=structure\n", "    )\n", "    \n", "    print(f\"✅ Surface energy of {ind}: {gamma:.3f} J/m²\")"]}, {"cell_type": "code", "execution_count": 28, "id": "a39c58c6-f607-4408-ac0c-f7396b43bc8e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/pymatgen/analysis/wulff.py:313: UserWarning: FigureCanvasAgg is non-interactive, and thus cannot be shown\n", "  self.get_plot(*args, **kwargs).get_figure().show()\n"]}, {"data": {"image/png": "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******************************+/rMtXrkhmWm7HPSq3bpHMVNyj4TYRfLXH8ObljB5XeuaKTDOlbz+wX0899RShBywg+IBlMjw8rFdeeUX50VFFKWdhefMGljfXAIKvdhjlkuzxk7ImL8gwDN13724999xz3CkDuAnBByyzs2fP6tVXf6qZmWmFVlZurl9+Yy+73VYxgq8GBGXZ40OyJ87IiCJt27ZVAwMDyma5tzFwKwQfsEKOHj2qN37+cxULBQVO08Ly5k7CbxUi+Fax0Jc9ea6yNDn01bd+vV566SW1tLTEPRmwqhF8wAr78MMP9favf6Oy58rPti/cn7Ut7rFwA4JvFYpCWVMXZY+ekBG4ynV2amBgQD09PXFPBtQEgg+IQRiGeuedd/T+B79XGPgqN3TLy+*************************************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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pymatgen.analysis.wulff import WulffShape\n", "\n", "# 1. Miller indices 및 대응하는 표면 에너지 (J/m²)\n", "surface_energies = {\n", "    (1, 0, 0): 3.141,\n", "    (1, 1, 0): 0.579,\n", "    (1, 1, 1): 3.170,\n", "    (2, 1, 0): 1.378\n", "}\n", "\n", "# 2. <PERSON><PERSON><PERSON> shape 생성\n", "wulff = WulffShape(\n", "    lattice=structure.lattice,\n", "    miller_list=list(surface_energies.keys()),\n", "    e_surf_list=list(surface_energies.values())\n", ")\n", "\n", "# 3. 시각화\n", "wulff.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}
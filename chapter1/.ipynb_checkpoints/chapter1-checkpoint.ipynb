{"cells": [{"cell_type": "markdown", "id": "7c27ff54-3aea-4d91-9ac9-6b706663a109", "metadata": {}, "source": ["# Materials Project에서 구조 가져오기"]}, {"cell_type": "code", "execution_count": 1, "id": "689050a1-8cb5-42d4-af82-3100bc608b41", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Material ID: mp-1097885\n", "Full Formula (Li4 Co4 O8)\n", "Reduced Formula: LiCoO2\n", "abc   :   5.813942   5.847535   5.824804\n", "angles: 120.454930  91.037515  59.370262\n", "pbc   :       True       True       True\n", "Sites (16)\n", "  #  SP           a         b         c    magmom\n", "---  ----  --------  --------  --------  --------\n", "  0  Li    0.500014  1e-06     0.999987     0\n", "  1  Li    0.500014  0.499993  0.499977     0\n", "  2  Li    2e-05     0.999992  0.999982     0\n", "  3  Li    0.50001   0.500001  0.999983     0\n", "  4  Co    1.1e-05   0.499997  0.999986     0.003\n", "  5  Co    1e-05     0.499998  0.499986     0.003\n", "  6  Co    0.500011  0.999997  0.499986    -0.002\n", "  7  Co    1.1e-05   0.999998  0.499986     0.003\n", "  8  O     0.742938  0.995504  0.268089    -0.001\n", "  9  O     0.748705  0.523526  0.259977    -0.001\n", " 10  O     0.226928  0.512587  0.246241    -0.002\n", " 11  O     0.750769  0.985719  0.729724    -0\n", " 12  O     0.249253  0.014275  0.270248    -0\n", " 13  O     0.773094  0.487408  0.753731    -0.002\n", " 14  O     0.251318  0.476467  0.739993    -0.001\n", " 15  O     0.257086  0.00449   0.731883    -0.001\n"]}], "source": ["from pymatgen.ext.matproj import MPRester  \n", "\n", "API_KEY = \"NlY7ehbJQwKy9ZTmXlrpz46SMY6HZOpO\"\n", "\n", "with MPRester(API_KEY) as mpr:\n", "    # 원하는 구조 데이터를 검색 후 반환\n", "    search_results = mpr.summary.search(formula=\"LiCoO2\")\n", "    if search_results:\n", "        material_id = search_results[0]['material_id']\n", "        structure = mpr.get_structure_by_material_id(material_id)\n", "\n", "        print(f\"Material ID: {material_id}\")\n", "        print(structure)\n", "    else:\n", "        print(\"LiCoO2 관련 구조를 찾을 수 없습니다.\")"]}, {"cell_type": "markdown", "id": "5bd0bf68-fe52-42c8-b11d-6b2254a4ef2c", "metadata": {}, "source": ["# VASP 및 MLP를 이용한 에너지 계산"]}, {"cell_type": "markdown", "id": "cd8b2ea7-815c-4b27-9c44-9af24245749d", "metadata": {}, "source": ["VASP을 이용해서 계산할 경우"]}, {"cell_type": "code", "execution_count": 2, "id": "90be262b-190c-465f-bfcf-4e44b032bf23", "metadata": {}, "outputs": [], "source": ["# # VASP input generation and...\n", "# from pymatgen.io.vasp.sets import MPRelaxSet\n", "# vasp_input_set = MPRelaxSet(structure)\n", "# vasp_input_set.write_input(\"./LiCoO2_vasp_input\")\n", "\n", "# # VASP 계산이 끝난 후\n", "# from pymatgen.io.vasp.outputs import Oszicar\n", "\n", "# output_structure = Structure.from_file(\"CONTCAR\")\n", "# oszicar = Oszicar(\"OSZICAR\")\n", "\n", "# # 에너지 히스토리\n", "# energies = [step[\"E0\"] for step in oszicar.ionic_steps]\n", "\n", "# print(\"OSZICAR 기반 에너지 트래젝토리:\")\n", "# for i, e in enumerate(energies):\n", "#     print(f\"Step {i}: Energy = {e:.6f} eV\")"]}, {"cell_type": "markdown", "id": "862c4b85-88f6-45c3-aefb-bba2875e2975", "metadata": {}, "source": ["CHGNET을 이용해서 계산할 경우 (비교적 부정확! 실제 연구에서는 짧은 경향성 확인에만 사용하고, 최종 계산은 DFT 계산을 사용할 것!)"]}, {"cell_type": "code", "execution_count": 3, "id": "d7070dd3-1377-437f-b305-aa5b4f4a40b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on cuda\n", "      Step     Time          Energy          fmax\n", "FIRE:    0 14:16:23     -101.644730        0.236548\n", "FIRE:    1 14:16:23     -101.647438        0.223708\n", "FIRE:    2 14:16:23     -101.652245        0.185662\n", "FIRE:    3 14:16:23     -101.657608        0.115274\n", "FIRE:    4 14:16:23     -101.660759        0.082837\n", "CHGNet relaxed structure Full Formula (Li4 Co4 O8)\n", "Reduced Formula: LiCoO2\n", "abc   :   5.812262   5.845798   5.823343\n", "angles: 120.452172  91.027619  59.379102\n", "pbc   :       True       True       True\n", "Sites (16)\n", "  #  SP           a         b         c      magmom\n", "---  ----  --------  --------  --------  ----------\n", "  0  Li    0.500014  1e-06     0.999987  0.00157833\n", "  1  Li    0.500014  0.499993  0.499976  0.00179097\n", "  2  Li    2e-05     0.999992  0.999982  0.0033848\n", "  3  Li    0.50001   0.500001  0.999983  0.00164181\n", "  4  Co    1.2e-05   0.499997  0.999986  1.06859\n", "  5  Co    1.1e-05   0.499997  0.499986  0.927139\n", "  6  Co    0.500011  0.999997  0.499986  0.0079459\n", "  7  Co    1.1e-05   0.999997  0.499986  0.915436\n", "  8  O     0.745206  0.994382  0.269119  0.03537\n", "  9  O     0.749815  0.524056  0.26207   0.0353508\n", " 10  O     0.225698  0.514267  0.2497    0.0548142\n", " 11  O     0.753834  0.982777  0.725817  0.0365309\n", " 12  O     0.246188  0.017217  0.274156  0.0365313\n", " 13  O     0.774324  0.485728  0.750272  0.0548145\n", " 14  O     0.250207  0.475937  0.737901  0.0353509\n", " 15  O     0.254816  0.005612  0.730853  0.0353708\n", "relaxed total energy in eV: -101.66076\n"]}], "source": ["# CHGNET atomic relaxation\n", "\n", "from chgnet.model import StructOptimizer\n", "\n", "relaxer = StructOptimizer()\n", "result = relaxer.relax(structure)\n", "print(\"CHGNet relaxed structure\", result[\"final_structure\"])\n", "print(\"relaxed total energy in eV:\", result['trajectory'].energies[-1])"]}, {"cell_type": "code", "execution_count": 4, "id": "324605ed-100a-4d14-a9e4-1622b679ab2f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'final_structure': Structure Summary\n", " <PERSON><PERSON><PERSON>\n", "     abc : 5.812261921316214 5.8457976747835145 5.823343044131806\n", "  angles : 120.45217181471439 91.02761925199151 59.379102007788894\n", "  volume : 138.87420830907192\n", "       A : np.float64(-0.05213317430248843) np.float64(3.3503092210753223) np.float64(-4.7492208726587215)\n", "       B : np.float64(-2.892071723618088) np.float64(5.080202513597476) np.float64(-0.028531049593871153)\n", "       C : np.float64(5.822771579444645) np.float64(-0.08131839796372523) np.float64(0.006531569690392391)\n", "     pbc : True True True\n", " PeriodicSite: Li (5.797, 1.594, -2.368) [0.5, 1.123e-06, 1.0]\n", " PeriodicSite: Li (1.439, 4.175, -2.386) [0.5, 0.5, 0.5]\n", " PeriodicSite: Li (2.931, 4.999, -0.0221) [2.048e-05, 1.0, 1.0]\n", " PeriodicSite: Li (4.351, 4.134, -2.382) [0.5, 0.5, 1.0]\n", " PeriodicSite: Co (4.377, 2.459, -0.007789) [1.151e-05, 0.5, 1.0]\n", " PeriodicSite: Co (1.465, 2.499, -0.01105) [1.104e-05, 0.5, 0.5]\n", " PeriodicSite: Co (-0.006828, 6.715, -2.4) [0.5, 1.0, 0.5]\n", " PeriodicSite: Co (0.01924, 5.04, -0.02532) [1.118e-05, 1.0, 0.5]\n", " PeriodicSite: O (-1.348, 7.526, -3.566) [0.7452, 0.9944, 0.2691]\n", " PeriodicSite: O (-0.02872, 5.153, -3.574) [0.7498, 0.5241, 0.2621]\n", " PeriodicSite: O (-0.04512, 3.348, -1.085) [0.2257, 0.5143, 0.2497]\n", " PeriodicSite: O (1.345, 7.459, -3.603) [0.7538, 0.9828, 0.7258]\n", " PeriodicSite: O (1.534, 0.89, -1.168) [0.2462, 0.01722, 0.2742]\n", " PeriodicSite: O (2.924, 5.001, -3.686) [0.7743, 0.4857, 0.7503]\n", " PeriodicSite: O (2.907, 3.196, -1.197) [0.2502, 0.4759, 0.7379]\n", " PeriodicSite: O (4.226, 0.8228, -1.206) [0.2548, 0.005612, 0.7309],\n", " 'trajectory': <chgnet.model.dynamics.TrajectoryObserver at 0x2af6870e56a0>}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 5, "id": "be2ddd69-a3f6-44af-a0de-9a27f2a3cc94", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"# generated using pymatgen\\ndata_LiCoO2\\n_symmetry_space_group_name_H-M   'P 1'\\n_cell_length_a   5.81226192\\n_cell_length_b   5.84579767\\n_cell_length_c   5.82334304\\n_cell_angle_alpha   120.45217181\\n_cell_angle_beta   91.02761925\\n_cell_angle_gamma   59.37910201\\n_symmetry_Int_Tables_number   1\\n_chemical_formula_structural   LiCoO2\\n_chemical_formula_sum   'Li4 Co4 O8'\\n_cell_volume   138.87420831\\n_cell_formula_units_Z   4\\nloop_\\n _symmetry_equiv_pos_site_id\\n _symmetry_equiv_pos_as_xyz\\n  1  'x, y, z'\\nloop_\\n _atom_site_type_symbol\\n _atom_site_label\\n _atom_site_symmetry_multiplicity\\n _atom_site_fract_x\\n _atom_site_fract_y\\n _atom_site_fract_z\\n _atom_site_occupancy\\n  Li  Li0  1  0.50001419  0.00000112  0.99998680  1\\n  Li  Li1  1  0.50001400  0.49999279  0.49997631  1\\n  Li  Li2  1  0.00002048  0.99999198  0.99998167  1\\n  Li  Li3  1  0.50001016  0.50000096  0.99998263  1\\n  Co  Co4  1  0.00001151  0.49999719  0.99998573  1\\n  Co  Co5  1  0.00001104  0.49999699  0.49998591  1\\n  Co  Co6  1  0.50001099  0.99999741  0.49998593  1\\n  Co  Co7  1  0.00001118  0.99999737  0.49998590  1\\n  O  O8  1  0.74520623  0.99438178  0.26911906  1\\n  O  O9  1  0.74981509  0.52405642  0.26207007  1\\n  O  O10  1  0.22569776  0.51426707  0.24970019  1\\n  O  O11  1  0.75383411  0.98277706  0.72581655  1\\n  O  O12  1  0.24618779  0.01721738  0.27415587  1\\n  O  O13  1  0.77432398  0.48572784  0.75027222  1\\n  O  O14  1  0.25020718  0.47593722  0.73790098  1\\n  O  O15  1  0.25481632  0.00561240  0.73085318  1\\n\""]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result['final_structure'].to('./relaxed.cif')"]}, {"cell_type": "code", "execution_count": 6, "id": "9d725ad6-7192-4b6d-b246-d48743e45a48", "metadata": {}, "outputs": [], "source": ["final_structure = result['final_structure']"]}, {"cell_type": "markdown", "id": "11c2eaa9-e5f6-4b55-a947-2a750c7adda4", "metadata": {}, "source": ["# 가장 간단한 transformation 해 보기"]}, {"cell_type": "code", "execution_count": 7, "id": "85517b8d-8943-4697-a348-3c0c94ec1b79", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 변형된 격자:\n", "-0.052655 3.383812 -4.796713\n", "-2.920992 5.131005 -0.028816\n", "5.880999 -0.082132 0.006597\n"]}], "source": ["from pymatgen.transformations.standard_transformations import DeformStructureTransformation\n", "import numpy as np\n", "\n", "# 예시: 1% strain을 대각 성분에 적용 (미세한 등방성 인장)\n", "deformation_matrix = np.array([\n", "    [1.01, 0.00, 0.00],\n", "    [0.00, 1.01, 0.00],\n", "    [0.00, 0.00, 1.01]\n", "])\n", "\n", "# 변형 트랜스포머 정의\n", "deform = DeformStructureTransformation(deformation_matrix)\n", "\n", "# 변형 구조 생성\n", "deformed_structure = deform.apply_transformation(final_structure)\n", "\n", "# 결과 확인\n", "print(\"✅ 변형된 격자:\")\n", "print(deformed_structure.lattice)"]}, {"cell_type": "code", "execution_count": 9, "id": "42ca4b51-f4c3-4abd-bc44-50a325dcdcdc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on cuda\n", "CHGNet-predicted energy (eV/atom):\n", "-6.353797435760498\n", "\n", "CHGNet-predicted forces (eV/A):\n", "[[-6.9384696e-06  5.7150028e-06 -6.5828208e-06]\n", " [-1.7050654e-05 -9.0890971e-06 -7.8522135e-06]\n", " [-2.2036256e-05  1.5182071e-05 -1.5923812e-05]\n", " [-1.5833415e-05  1.2157310e-05 -4.0143495e-06]\n", " [ 2.4333596e-05 -3.0670315e-05  5.7755038e-05]\n", " [-3.1151343e-05  2.4797861e-05  2.3883302e-05]\n", " [ 4.5013847e-05 -2.9015355e-05 -2.7444679e-05]\n", " [ 2.4433015e-05 -2.4841866e-05  2.4631619e-05]\n", " [ 3.2813221e-02 -3.2025933e-02 -6.8990067e-02]\n", " [ 2.9109903e-02  2.4874449e-02 -2.1078898e-02]\n", " [ 1.6180567e-02 -1.6248822e-02  8.2335332e-03]\n", " [-5.1611803e-02 -3.3498168e-02 -7.6282322e-03]\n", " [ 5.1583856e-02  3.3494160e-02  7.6203421e-03]\n", " [-1.6189858e-02  1.6237725e-02 -8.2632601e-03]\n", " [-2.9073097e-02 -2.4830543e-02  2.1060744e-02]\n", " [-3.2812938e-02  3.2032955e-02  6.9001332e-02]]\n", "\n", "CHGNet-predicted stress (GPa):\n", "[[ 0.84499925  0.06379616 -0.06609288]\n", " [ 0.06379461  0.85763025 -0.12593253]\n", " [-0.06609201 -0.12593256  0.7748497 ]]\n", "\n", "CHGNet-predicted magmom (mu_B):\n", "[0.00157827 0.001791   0.00338477 0.00164187 1.0685922  0.92713904\n", " 0.00794569 0.9154362  0.03536987 0.03535086 0.05481416 0.03653097\n", " 0.03653115 0.05481434 0.03535104 0.03537095]\n", "\n", "CHGNet-predicted energy (eV/atom):\n", "-6.352097988128662\n", "\n", "CHGNet-predicted forces (eV/A):\n", "[[-1.63849327e-05  1.54941226e-05 -1.49722910e-05]\n", " [-3.00543616e-05 -3.21722473e-06 -2.98779923e-06]\n", " [-1.31021952e-05  9.85106453e-07 -2.32861494e-05]\n", " [-1.39045878e-05  9.60647594e-06 -9.06636706e-06]\n", " [ 2.67438591e-05 -1.59405172e-05  2.27689743e-05]\n", " [ 2.89268792e-06  1.42621575e-05  1.20877521e-05]\n", " [ 2.82742549e-05 -1.87780242e-05 -2.28765421e-05]\n", " [ 1.80173665e-05 -1.44001096e-05  2.28853896e-05]\n", " [ 4.53641117e-02 -1.85295641e-02 -2.05900557e-02]\n", " [ 3.66806984e-02  4.80845571e-02  1.98515486e-02]\n", " [ 3.40266526e-02 -2.44795848e-02 -1.97713729e-02]\n", " [-7.25024790e-02 -3.01085506e-02 -1.32197440e-02]\n", " [ 7.24907815e-02  3.00799310e-02  1.32253692e-02]\n", " [-3.40348855e-02  2.44883224e-02  1.97424591e-02]\n", " [-3.66557091e-02 -4.80453894e-02 -1.98333710e-02]\n", " [-4.53715064e-02  1.85223557e-02  2.06108987e-02]]\n", "\n", "CHGNet-predicted stress (GPa):\n", "[[ 1.4391416  -0.00875519  0.00387015]\n", " [-0.00875265  1.4187896  -0.19330704]\n", " [ 0.00386961 -0.19330375  1.4654105 ]]\n", "\n", "CHGNet-predicted magmom (mu_B):\n", "[0.00307536 0.00336382 0.00513837 0.00312406 1.4100106  1.3469565\n", " 0.16051543 1.3411485  0.05294937 0.05206066 0.07483453 0.05351245\n", " 0.05351377 0.07483524 0.05206203 0.05294812]\n", "\n"]}], "source": ["from chgnet.model.model import CHGNet\n", "\n", "chgnet = CHGNet.load()\n", "prediction1 = chgnet.predict_structure(final_structure)\n", "\n", "for key, unit in [\n", "    (\"energy\", \"eV/atom\"),\n", "    (\"forces\", \"eV/A\"),\n", "    (\"stress\", \"GPa\"),\n", "    (\"magmom\", \"mu_B\"),\n", "]:\n", "    print(f\"CHGNet-predicted {key} ({unit}):\\n{prediction1[key[0]]}\\n\")\n", "\n", "prediction2 = chgnet.predict_structure(deformed_structure)\n", "\n", "for key, unit in [\n", "    (\"energy\", \"eV/atom\"),\n", "    (\"forces\", \"eV/A\"),\n", "    (\"stress\", \"GPa\"),\n", "    (\"magmom\", \"mu_B\"),\n", "]:\n", "    print(f\"CHGNet-predicted {key} ({unit}):\\n{prediction2[key[0]]}\\n\")"]}, {"cell_type": "markdown", "id": "7bc29a10-b777-4d21-b4a9-3444e130dd06", "metadata": {}, "source": ["# 원자가 일부 지워진 구조 여러 개 생성 및 에너지 평가"]}, {"cell_type": "code", "execution_count": 23, "id": "3ab11cea-cd04-4518-ae98-df55a8c41a0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Structure 1: Energy ranking = -3232.5335 (relative)\n", "Structure 2: Energy ranking = -3232.0809 (relative)\n", "Structure 3: Energy ranking = -3232.0809 (relative)\n", "Structure 4: Energy ranking = -3232.0809 (relative)\n", "Structure 5: Energy ranking = -3232.0809 (relative)\n", "Structure 6: Energy ranking = -3231.9804 (relative)\n", "Structure 7: Energy ranking = -3231.9804 (relative)\n", "Structure 8: Energy ranking = -3231.9735 (relative)\n", "Structure 9: Energy ranking = -3231.9735 (relative)\n", "Structure 10: Energy ranking = -3231.8942 (relative)\n"]}], "source": ["from pymatgen.transformations.standard_transformations import AutoOxiStateDecorationTransformation\n", "from pymatgen.transformations.standard_transformations import PartialRemoveSitesTransformation\n", "\n", "# 1. 2×2×2 supercell 생성\n", "supercell = final_structure.copy()\n", "supercell.make_supercell([2, 2, 2])  # 8배 확장\n", "\n", "# 2. 구조에 산화수 부여 (in-place 대체가 아닌 새 구조 반환)\n", "structure_with_oxi = AutoOxiStateDecorationTransformation().apply_transformation(supercell)\n", "\n", "# 3. Li site 인덱스 찾기\n", "li_indices = [i for i, site in enumerate(structure_with_oxi) if site.species_string == \"Li+\"]\n", "\n", "# 4. PartialRemoveSitesTransformation 정의\n", "# Li 중 절반(50%) 제거, electrostatic energy 기준 상위 10개만 유지\n", "t = PartialRemoveSitesTransformation(\n", "    [li_indices],\n", "    [0.5],\n", "    algo=0\n", ")\n", "\n", "# 5. 변환 적용 → 결손 구조 리스트 생성\n", "defect_structures = t.apply_transformation(structure_with_oxi, return_ranked_list=10)\n", "\n", "# 6. 결과 출력\n", "for i, entry in enumerate(defect_structures):\n", "    print(f\"Structure {i+1}: Energy ranking = {entry['energy']:.4f} (relative)\")"]}, {"cell_type": "code", "execution_count": 31, "id": "14a8c027-d4cf-4ea9-8c41-104c6346454a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on cuda\n", "Structure 1: MLP relaxed energy = -731.0240\n", "Structure 2: MLP relaxed energy = -731.3083\n", "Structure 3: MLP relaxed energy = -731.3083\n", "Structure 4: MLP relaxed energy = -731.3083\n", "Structure 5: MLP relaxed energy = -731.3083\n", "Structure 6: MLP relaxed energy = -731.3992\n", "Structure 7: MLP relaxed energy = -731.3992\n", "Structure 8: MLP relaxed energy = -731.3685\n", "Structure 9: MLP relaxed energy = -731.3685\n", "Structure 10: MLP relaxed energy = -731.1730\n"]}], "source": ["# 각 구조에 대한 MLP relaxation 에너지\n", "from chgnet.model import StructOptimizer\n", "relaxer = StructOptimizer()\n", "relaxed_structures = []\n", "for i, entry in enumerate(defect_structures):\n", "    result = relaxer.relax(entry['structure'], verbose=False)\n", "    relaxed_energy = result['trajectory'].energies[-1]\n", "    relaxed_structures.append(result['final_structure'])\n", "    print(f\"Structure {i+1}: MLP relaxed energy = {relaxed_energy:.4f}\")"]}, {"cell_type": "code", "execution_count": 32, "id": "35c6ce62-2112-4f32-a079-91d95f7f296b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"# generated using pymatgen\\ndata_Li(CoO2)2\\n_symmetry_space_group_name_H-M   'P 1'\\n_cell_length_a   11.60793435\\n_cell_length_b   11.68228360\\n_cell_length_c   11.63956994\\n_cell_angle_alpha   120.49852842\\n_cell_angle_beta   91.01316587\\n_cell_angle_gamma   59.41341788\\n_symmetry_Int_Tables_number   1\\n_chemical_formula_structural   Li(CoO2)2\\n_chemical_formula_sum   'Li16 Co32 O64'\\n_cell_volume   1107.50949027\\n_cell_formula_units_Z   16\\nloop_\\n _symmetry_equiv_pos_site_id\\n _symmetry_equiv_pos_as_xyz\\n  1  'x, y, z'\\nloop_\\n _atom_type_symbol\\n _atom_type_oxidation_number\\n  Li+  1.0\\n  Co3+  3.0\\n  O2-  -2.0\\nloop_\\n _atom_site_type_symbol\\n _atom_site_label\\n _atom_site_symmetry_multiplicity\\n _atom_site_fract_x\\n _atom_site_fract_y\\n _atom_site_fract_z\\n _atom_site_occupancy\\n  Li+  Li0  1  0.25000963  0.00000498  0.49999564  1\\n  Li+  Li1  1  0.25000955  0.50000523  0.99999572  1\\n  Li+  Li2  1  0.75000958  0.00000516  0.49999572  1\\n  Li+  Li3  1  0.75000940  0.50000537  0.99999583  1\\n  Li+  Li4  1  0.25001122  0.24998989  0.24997992  1\\n  Li+  Li5  1  0.25001123  0.74998988  0.74997997  1\\n  Li+  Li6  1  0.75001123  0.24998993  0.24998009  1\\n  Li+  Li7  1  0.75001116  0.74998992  0.74997999  1\\n  Li+  Li8  1  0.24970351  0.25518571  0.50518985  1\\n  Li+  Li9  1  0.25030444  0.24481773  0.99479217  1\\n  Li+  Li10  1  0.25030446  0.74481789  0.49479222  1\\n  Li+  Li11  1  0.24970352  0.75518567  1.00518983  1\\n  Li+  Li12  1  0.74970352  0.25518568  0.50518989  1\\n  Li+  Li13  1  0.75030440  0.24481778  0.99479223  1\\n  Li+  Li14  1  0.75030445  0.74481781  0.49479224  1\\n  Li+  Li15  1  0.74970354  0.75518561  1.00518977  1\\n  Co3+  Co16  1  -0.00134646  0.25287100  0.49933357  1\\n  Co3+  Co17  1  0.00135689  0.24712609  1.00065339  1\\n  Co3+  Co18  1  0.00135692  0.74712618  0.50065346  1\\n  Co3+  Co19  1  -0.00134640  0.75287095  0.99933357  1\\n  Co3+  Co20  1  0.49865357  0.25287092  0.49933355  1\\n  Co3+  Co21  1  0.50135693  0.24712611  1.00065337  1\\n  Co3+  Co22  1  0.50135698  0.74712611  0.50065336  1\\n  Co3+  Co23  1  0.49865343  0.75287110  0.99933358  1\\n  Co3+  Co24  1  0.00000558  0.24999864  0.24999350  1\\n  Co3+  Co25  1  0.00000529  0.24999838  0.74999304  1\\n  Co3+  Co26  1  0.00000538  0.74999825  0.24999298  1\\n  Co3+  Co27  1  0.00000545  0.74999869  0.74999351  1\\n  Co3+  Co28  1  0.50000545  0.24999873  0.24999348  1\\n  Co3+  Co29  1  0.50000523  0.24999842  0.74999301  1\\n  Co3+  Co30  1  0.50000531  0.74999836  0.24999306  1\\n  Co3+  Co31  1  0.50000553  0.74999878  0.74999342  1\\n  Co3+  Co32  1  0.24992834  0.50075942  0.25072133  1\\n  Co3+  Co33  1  0.25008208  0.49923800  0.74926572  1\\n  Co3+  Co34  1  0.25008208  0.99923803  0.24926562  1\\n  Co3+  Co35  1  0.24992846  1.00075932  0.75072114  1\\n  Co3+  Co36  1  0.74992841  0.50075933  0.25072124  1\\n  Co3+  Co37  1  0.75008204  0.49923804  0.74926573  1\\n  Co3+  Co38  1  0.75008207  0.99923798  0.24926561  1\\n  Co3+  Co39  1  0.74992843  1.00075935  0.75072120  1\\n  Co3+  Co40  1  -0.00155552  0.50223422  0.24895847  1\\n  Co3+  Co41  1  0.00156613  0.49776297  0.75102806  1\\n  Co3+  Co42  1  0.00156616  0.99776304  0.25102818  1\\n  Co3+  Co43  1  -0.00155542  1.00223423  0.74895852  1\\n  Co3+  Co44  1  0.49844459  0.50223425  0.24895851  1\\n  Co3+  Co45  1  0.50156609  0.49776290  0.75102807  1\\n  Co3+  Co46  1  0.50156608  0.99776306  0.25102821  1\\n  Co3+  Co47  1  0.49844451  1.00223430  0.74895862  1\\n  O2-  O48  1  0.38145002  0.47684238  0.12605800  1\\n  O2-  O49  1  0.38187309  0.49156470  0.64080345  1\\n  O2-  O50  1  0.38187311  0.99156459  0.14080326  1\\n  O2-  O51  1  0.38145024  0.97684246  0.62605804  1\\n  O2-  O52  1  0.88145012  0.47684243  0.12605803  1\\n  O2-  O53  1  0.88187324  0.49156467  0.64080330  1\\n  O2-  O54  1  0.88187332  0.99156461  0.14080327  1\\n  O2-  O55  1  0.88145032  0.97684226  0.62605791  1\\n  O2-  O56  1  0.37530880  0.26978202  0.13207585  1\\n  O2-  O57  1  0.37787921  0.27197856  0.62387193  1\\n  O2-  O58  1  0.37787894  0.77197870  0.12387206  1\\n  O2-  O59  1  0.37530889  0.76978196  0.63207581  1\\n  O2-  O60  1  0.87530882  0.26978200  0.13207588  1\\n  O2-  O61  1  0.87787897  0.27197860  0.62387205  1\\n  O2-  O62  1  0.87787907  0.77197868  0.12387205  1\\n  O2-  O63  1  0.87530889  0.76978181  0.63207564  1\\n  O2-  O64  1  0.10640320  0.27228384  0.13252766  1\\n  O2-  O65  1  0.10663815  0.26208300  0.62211254  1\\n  O2-  O66  1  0.10663814  0.76208296  0.12211251  1\\n  O2-  O67  1  0.10640318  0.77228376  0.63252773  1\\n  O2-  O68  1  0.60640321  0.27228391  0.13252758  1\\n  O2-  O69  1  0.60663812  0.26208303  0.62211253  1\\n  O2-  O70  1  0.60663807  0.76208305  0.12211260  1\\n  O2-  O71  1  0.60640327  0.77228379  0.63252757  1\\n  O2-  O72  1  0.37887908  0.49755490  0.35024286  1\\n  O2-  O73  1  0.37605253  0.49180001  0.85507808  1\\n  O2-  O74  1  0.37605236  0.99180010  0.35507815  1\\n  O2-  O75  1  0.37887920  0.99755487  0.85024290  1\\n  O2-  O76  1  0.87887910  0.49755493  0.35024287  1\\n  O2-  O77  1  0.87605238  0.49180022  0.85507814  1\\n  O2-  O78  1  0.87605245  0.99180000  0.35507802  1\\n  O2-  O79  1  0.87887902  0.99755497  0.85024290  1\\n  O2-  O80  1  0.12113163  0.00244276  0.14974378  1\\n  O2-  O81  1  0.12395821  0.00819665  0.64490823  1\\n  O2-  O82  1  0.12395818  0.50819690  0.14490837  1\\n  O2-  O83  1  0.12113155  0.50244274  0.64974380  1\\n  O2-  O84  1  0.62113159  0.00244264  0.14974372  1\\n  O2-  O85  1  0.62395799  0.00819680  0.64490832  1\\n  O2-  O86  1  0.62395809  0.50819687  0.14490835  1\\n  O2-  O87  1  0.62113164  0.50244269  0.64974373  1\\n  O2-  O88  1  0.39360741  0.22771328  0.36745857  1\\n  O2-  O89  1  0.39337242  0.23791390  0.87787365  1\\n  O2-  O90  1  0.39337246  0.73791389  0.37787360  1\\n  O2-  O91  1  0.39360741  0.72771330  0.86745853  1\\n  O2-  O92  1  0.89360750  0.22771320  0.36745842  1\\n  O2-  O93  1  0.89337241  0.23791394  0.87787364  1\\n  O2-  O94  1  0.89337237  0.73791393  0.37787365  1\\n  O2-  O95  1  0.89360745  0.72771334  0.86745845  1\\n  O2-  O96  1  0.12470213  0.23021492  0.36791125  1\\n  O2-  O97  1  0.12213148  0.22801857  0.87611360  1\\n  O2-  O98  1  0.12213130  0.72801874  0.37611368  1\\n  O2-  O99  1  0.12470222  0.73021488  0.86791126  1\\n  O2-  O100  1  0.62470215  0.23021491  0.36791115  1\\n  O2-  O101  1  0.62213131  0.22801877  0.87611385  1\\n  O2-  O102  1  0.62213121  0.72801877  0.37611390  1\\n  O2-  O103  1  0.62470208  0.73021496  0.86791117  1\\n  O2-  O104  1  0.11856129  0.02315410  0.37392822  1\\n  O2-  O105  1  0.11813791  0.00843270  0.85918320  1\\n  O2-  O106  1  0.11813798  0.50843284  0.35918338  1\\n  O2-  O107  1  0.11856116  0.52315411  0.87392820  1\\n  O2-  O108  1  0.61856120  0.02315420  0.37392826  1\\n  O2-  O109  1  0.61813792  0.00843288  0.85918333  1\\n  O2-  O110  1  0.61813776  0.50843288  0.35918336  1\\n  O2-  O111  1  0.61856135  0.52315403  0.87392815  1\\n\""]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["relaxed_structures[6].to('./relaxed_partial.cif')"]}, {"cell_type": "markdown", "id": "e76c1c95-58ca-4056-939b-177900b12b7d", "metadata": {}, "source": ["# Space Group 찾기"]}, {"cell_type": "code", "execution_count": 38, "id": "317cb283-583b-49fa-9efe-aff1af472a0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Space group symbol: P-1\n", "Space group number: 2\n", "International notation: P-1 (2) spacegroup\n"]}], "source": ["from pymatgen.symmetry.analyzer import SpacegroupAnalyzer\n", "\n", "# 공간군 분석기 생성\n", "sga = SpacegroupAnalyzer(relaxed_structures[6], symprec=1e-3)\n", "\n", "# 공간군 이름 및 번호 출력\n", "print(\"Space group symbol:\", sga.get_space_group_symbol())\n", "print(\"Space group number:\", sga.get_space_group_number())\n", "print(\"International notation:\", sga.get_space_group_operations())"]}, {"cell_type": "markdown", "id": "407fa5a7-9616-41af-9683-c255675c0a48", "metadata": {}, "source": ["# 구조 유사성 검사"]}, {"cell_type": "code", "execution_count": 42, "id": "b5eb2e7a-a67f-4ea0-90cd-1ddbc137e2d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 구조 유사성: 다름\n"]}], "source": ["from pymatgen.analysis.structure_matcher import StructureMatcher\n", "\n", "# 구조 비교기 생성\n", "matcher = StructureMatcher()\n", "\n", "# 두 구조가 유사한지 판단\n", "is_match = matcher.fit(relaxed_structures[4], relaxed_structures[6])\n", "\n", "print(\"🔍 구조 유사성:\", \"유사함\" if is_match else \"다름\")"]}, {"cell_type": "code", "execution_count": null, "id": "8e8b2b22-fa0f-46cd-b13d-1d21e5db222e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}
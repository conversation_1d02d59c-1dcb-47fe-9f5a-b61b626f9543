{"cells": [{"cell_type": "markdown", "id": "7c27ff54-3aea-4d91-9ac9-6b706663a109", "metadata": {}, "source": ["# Materials Project에서 구조 가져오기"]}, {"cell_type": "code", "execution_count": 5, "id": "689050a1-8cb5-42d4-af82-3100bc608b41", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Material ID: mp-559432\n", "Full Formula (C6 F12)\n", "Reduced Formula: CF2\n", "abc   :   6.206991   6.373727   6.723793\n", "angles:  92.341487 109.080032  94.328064\n", "pbc   :       True       True       True\n", "Sites (18)\n", "  #  SP           a         b         c    magmom\n", "---  ----  --------  --------  --------  --------\n", "  0  C     0.548277  0.825989  0.767018        -0\n", "  1  C     0.451723  0.174011  0.232982        -0\n", "  2  C     0.344624  0.743042  0.634611        -0\n", "  3  C     0.655376  0.256958  0.365389        -0\n", "  4  C     0.254615  0.288284  0.108417        -0\n", "  5  C     0.745385  0.711716  0.891583        -0\n", "  6  F     0.692815  0.499595  0.870842        -0\n", "  7  F     0.720424  0.460936  0.409106         0\n", "  8  F     0.812931  0.774941  0.100638        -0\n", "  9  F     0.931608  0.753553  0.828328         0\n", " 10  F     0.307185  0.500405  0.129158        -0\n", " 11  F     0.187069  0.225059  0.899362        -0\n", " 12  F     0.068392  0.246447  0.171672         0\n", " 13  F     0.59109   0.039018  0.795363         0\n", " 14  F     0.183879  0.861385  0.530871        -0\n", " 15  F     0.279576  0.539064  0.590894         0\n", " 16  F     0.816121  0.138615  0.469129        -0\n", " 17  F     0.40891   0.960982  0.204637         0\n"]}], "source": ["from pymatgen.ext.matproj import MPRester  \n", "\n", "API_KEY = \"cwJruOX65TSz5C6KwWZTIj6BCHLoodVq\"\n", "\n", "with MPRester(API_KEY) as mpr:\n", "    # 원하는 구조 데이터를 검색 후 반환\n", "    search_results = mpr.summary.search(formula=\"C4F8\")\n", "    if search_results:\n", "        material_id = search_results[0]['material_id']\n", "        structure = mpr.get_structure_by_material_id(material_id)\n", "\n", "        print(f\"Material ID: {material_id}\")\n", "        print(structure)\n", "    else:\n", "        print(\"LiCoO2 관련 구조를 찾을 수 없습니다.\")"]}, {"cell_type": "markdown", "id": "5bd0bf68-fe52-42c8-b11d-6b2254a4ef2c", "metadata": {}, "source": ["# VASP 및 MLP를 이용한 에너지 계산"]}, {"cell_type": "markdown", "id": "cd8b2ea7-815c-4b27-9c44-9af24245749d", "metadata": {}, "source": ["VASP을 이용해서 계산할 경우"]}, {"cell_type": "code", "execution_count": 2, "id": "90be262b-190c-465f-bfcf-4e44b032bf23", "metadata": {}, "outputs": [], "source": ["# # VASP input generation and...\n", "# from pymatgen.io.vasp.sets import MPRelaxSet\n", "# vasp_input_set = MPRelaxSet(structure)\n", "# vasp_input_set.write_input(\"./LiCoO2_vasp_input\")\n", "\n", "# # VASP 계산이 끝난 후\n", "# from pymatgen.io.vasp.outputs import Oszicar\n", "\n", "# output_structure = Structure.from_file(\"CONTCAR\")\n", "# oszicar = Oszicar(\"OSZICAR\")\n", "\n", "# # 에너지 히스토리\n", "# energies = [step[\"E0\"] for step in oszicar.ionic_steps]\n", "\n", "# print(\"OSZICAR 기반 에너지 트래젝토리:\")\n", "# for i, e in enumerate(energies):\n", "#     print(f\"Step {i}: Energy = {e:.6f} eV\")"]}, {"cell_type": "markdown", "id": "862c4b85-88f6-45c3-aefb-bba2875e2975", "metadata": {}, "source": ["CHGNET을 이용해서 계산할 경우 (비교적 부정확! 실제 연구에서는 짧은 경향성 확인에만 사용하고, 최종 계산은 DFT 계산을 사용할 것!)"]}, {"cell_type": "code", "execution_count": 6, "id": "d7070dd3-1377-437f-b305-aa5b4f4a40b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on mps\n", "      Step     Time          Energy          fmax\n", "FIRE:    0 15:19:32     -106.295694        0.949493\n", "FIRE:    1 15:19:32     -106.310595        0.321229\n", "FIRE:    2 15:19:32     -106.308191        0.620869\n", "FIRE:    3 15:19:32     -106.311461        0.504474\n", "FIRE:    4 15:19:32     -106.315392        0.307845\n", "FIRE:    5 15:19:32     -106.317307        0.181988\n", "FIRE:    6 15:19:32     -106.317040        0.399264\n", "FIRE:    7 15:19:32     -106.317289        0.380483\n", "FIRE:    8 15:19:32     -106.317744        0.343429\n", "FIRE:    9 15:19:33     -106.318319        0.289823\n", "FIRE:   10 15:19:33     -106.318920        0.223845\n", "FIRE:   11 15:19:33     -106.319444        0.151637\n", "FIRE:   12 15:19:33     -106.319864        0.096331\n", "CHGNet relaxed structure Full Formula (C6 F12)\n", "Reduced Formula: CF2\n", "abc   :   6.207333   6.375130   6.725032\n", "angles:  92.340403 109.088128  94.327667\n", "pbc   :       True       True       True\n", "Sites (18)\n", "  #  SP           a         b         c      magmom\n", "---  ----  --------  --------  --------  ----------\n", "  0  C     0.548881  0.828993  0.767502  0.00644359\n", "  1  C     0.451119  0.171007  0.232498  0.0064436\n", "  2  C     0.345599  0.742514  0.635051  0.0051071\n", "  3  C     0.654401  0.257486  0.364949  0.00510706\n", "  4  C     0.255202  0.28851   0.108517  0.00229156\n", "  5  C     0.744798  0.71149   0.891483  0.00229178\n", "  6  F     0.692403  0.499174  0.870645  0.00403088\n", "  7  F     0.720283  0.461176  0.408947  0.0104183\n", "  8  F     0.812805  0.774427  0.100842  0.00140771\n", "  9  F     0.93118   0.753037  0.827997  0.0012143\n", " 10  F     0.307597  0.500826  0.129355  0.00403064\n", " 11  F     0.187195  0.225573  0.899158  0.0014081\n", " 12  F     0.06882   0.246963  0.172003  0.00121433\n", " 13  F     0.590745  0.039119  0.795094  0.00166661\n", " 14  F     0.184122  0.860515  0.53101   0.0124844\n", " 15  F     0.279717  0.538824  0.591053  0.0104186\n", " 16  F     0.815878  0.139485  0.46899   0.0124845\n", " 17  F     0.409255  0.960881  0.204906  0.00166649\n", "relaxed total energy in eV: -106.31986427307129\n"]}], "source": ["# CHGNET atomic relaxation\n", "\n", "from chgnet.model import StructOptimizer\n", "\n", "relaxer = StructOptimizer()\n", "result = relaxer.relax(structure)\n", "print(\"CHGNet relaxed structure\", result[\"final_structure\"])\n", "print(\"relaxed total energy in eV:\", result['trajectory'].energies[-1])"]}, {"cell_type": "code", "execution_count": 7, "id": "324605ed-100a-4d14-a9e4-1622b679ab2f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'final_structure': Structure Summary\n", " <PERSON><PERSON><PERSON>\n", "     abc : 6.207333104651702 6.3751298882802745 6.7250323648337895\n", "  angles : 92.34040287222251 109.08812766812787 94.3276672578179\n", "  volume : 250.1703263356843\n", "       A : 6.207333077543553 -1.7247392839934854e-05 -0.0005798630392259699\n", "       B : -0.48105118457424645 6.35695452571933 9.055772573220726e-05\n", "       C : -2.198643097551779 -0.44187986087550524 6.340092351551554\n", "     pbc : True True True\n", " PeriodicSite: C (1.321, 4.931, 4.866) [0.5489, 0.829, 0.7675]\n", " PeriodicSite: C (2.207, 0.9843, 1.474) [0.4511, 0.171, 0.2325]\n", " PeriodicSite: C (0.3918, 4.44, 4.026) [0.3456, 0.7425, 0.6351]\n", " PeriodicSite: C (3.136, 1.476, 2.313) [0.6544, 0.2575, 0.3649]\n", " PeriodicSite: C (1.207, 1.786, 0.6879) [0.2552, 0.2885, 0.1085]\n", " PeriodicSite: C (2.321, 4.129, 5.652) [0.7448, 0.7115, 0.8915]\n", " PeriodicSite: F (2.144, 2.788, 5.52) [0.6924, 0.4992, 0.8706]\n", " PeriodicSite: F (3.35, 2.751, 2.592) [0.7203, 0.4612, 0.4089]\n", " PeriodicSite: F (4.451, 4.878, 0.6389) [0.8128, 0.7744, 0.1008]\n", " PeriodicSite: F (3.597, 4.421, 5.249) [0.9312, 0.753, 0.828]\n", " PeriodicSite: F (1.384, 3.127, 0.82) [0.3076, 0.5008, 0.1294]\n", " PeriodicSite: F (-0.9235, 1.037, 5.701) [0.1872, 0.2256, 0.8992]\n", " PeriodicSite: F (-0.06978, 1.494, 1.09) [0.06882, 0.247, 0.172]\n", " PeriodicSite: F (1.9, -0.1027, 5.041) [0.5907, 0.03912, 0.7951]\n", " PeriodicSite: F (-0.4385, 5.236, 3.367) [0.1841, 0.8605, 0.531]\n", " PeriodicSite: F (0.1776, 3.164, 3.747) [0.2797, 0.5388, 0.5911]\n", " PeriodicSite: F (3.966, 0.6794, 2.973) [0.8159, 0.1395, 0.469]\n", " PeriodicSite: F (1.628, 6.018, 1.299) [0.4093, 0.9609, 0.2049],\n", " 'trajectory': <chgnet.model.dynamics.TrajectoryObserver at 0x177d0b9b0>}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 8, "id": "be2ddd69-a3f6-44af-a0de-9a27f2a3cc94", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"# generated using pymatgen\\ndata_CF2\\n_symmetry_space_group_name_H-M   'P 1'\\n_cell_length_a   6.20733310\\n_cell_length_b   6.37512989\\n_cell_length_c   6.72503236\\n_cell_angle_alpha   92.34040287\\n_cell_angle_beta   109.08812767\\n_cell_angle_gamma   94.32766726\\n_symmetry_Int_Tables_number   1\\n_chemical_formula_structural   CF2\\n_chemical_formula_sum   'C6 F12'\\n_cell_volume   250.17032634\\n_cell_formula_units_Z   6\\nloop_\\n _symmetry_equiv_pos_site_id\\n _symmetry_equiv_pos_as_xyz\\n  1  'x, y, z'\\nloop_\\n _atom_site_type_symbol\\n _atom_site_label\\n _atom_site_symmetry_multiplicity\\n _atom_site_fract_x\\n _atom_site_fract_y\\n _atom_site_fract_z\\n _atom_site_occupancy\\n  C  C0  1  0.54888130  0.82899327  0.76750184  1\\n  C  C1  1  0.45111870  0.17100672  0.23249817  1\\n  C  C2  1  0.34559926  0.74251362  0.63505116  1\\n  C  C3  1  0.65440072  0.25748637  0.36494883  1\\n  C  C4  1  0.25520216  0.28851012  0.10851698  1\\n  C  C5  1  0.74479784  0.71148988  0.89148299  1\\n  F  F6  1  0.69240332  0.49917431  0.87064544  1\\n  F  F7  1  0.72028349  0.46117597  0.40894667  1\\n  F  F8  1  0.81280514  0.77442700  0.10084236  1\\n  F  F9  1  0.93117958  0.75303666  0.82799731  1\\n  F  F10  1  0.30759669  0.50082568  0.12935457  1\\n  F  F11  1  0.18719487  0.22557301  0.89915764  1\\n  F  F12  1  0.06882041  0.24696333  0.17200271  1\\n  F  F13  1  0.59074516  0.03911944  0.79509370  1\\n  F  F14  1  0.18412167  0.86051495  0.53100993  1\\n  F  F15  1  0.27971651  0.53882404  0.59105332  1\\n  F  F16  1  0.81587835  0.13948504  0.46899008  1\\n  F  F17  1  0.40925484  0.96088057  0.20490631  1\\n\""]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["result['final_structure'].to('./relaxed.cif')"]}, {"cell_type": "code", "execution_count": 9, "id": "9d725ad6-7192-4b6d-b246-d48743e45a48", "metadata": {}, "outputs": [], "source": ["final_structure = result['final_structure']"]}, {"cell_type": "markdown", "id": "11c2eaa9-e5f6-4b55-a947-2a750c7adda4", "metadata": {}, "source": ["# 가장 간단한 transformation 해 보기"]}, {"cell_type": "code", "execution_count": 10, "id": "85517b8d-8943-4697-a348-3c0c94ec1b79", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 변형된 격자:\n", "6.269406 -0.000017 -0.000586\n", "-0.485862 6.420524 0.000091\n", "-2.220630 -0.446299 6.403493\n"]}], "source": ["from pymatgen.transformations.standard_transformations import DeformStructureTransformation\n", "import numpy as np\n", "\n", "# 예시: 1% strain을 대각 성분에 적용 (미세한 등방성 인장)\n", "deformation_matrix = np.array([\n", "    [1.01, 0.00, 0.00],\n", "    [0.00, 1.01, 0.00],\n", "    [0.00, 0.00, 1.01]\n", "])\n", "\n", "# 변형 트랜스포머 정의\n", "deform = DeformStructureTransformation(deformation_matrix)\n", "\n", "# 변형 구조 생성\n", "deformed_structure = deform.apply_transformation(final_structure)\n", "\n", "# 결과 확인\n", "print(\"✅ 변형된 격자:\")\n", "print(deformed_structure.lattice)"]}, {"cell_type": "code", "execution_count": 11, "id": "42ca4b51-f4c3-4abd-bc44-50a325dcdcdc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on mps\n", "CHGNet-predicted energy (eV/atom):\n", "-5.906659126281738\n", "\n", "CHGNet-predicted forces (eV/A):\n", "[[ 0.00600851  0.07572825  0.02387423]\n", " [-0.00600903 -0.07572123 -0.02387165]\n", " [ 0.08280003  0.00197291  0.04919231]\n", " [-0.08280194 -0.00195178 -0.04919267]\n", " [ 0.04056108 -0.00076145  0.00037938]\n", " [-0.04056966  0.00078696 -0.00036138]\n", " [-0.04431307 -0.00126374 -0.02527809]\n", " [-0.03658501  0.02293575 -0.03586571]\n", " [-0.00928855 -0.04814348  0.00082934]\n", " [-0.03730656 -0.03804456 -0.02731758]\n", " [ 0.04431427  0.00124323  0.02527714]\n", " [ 0.00928491  0.04814124 -0.00084025]\n", " [ 0.03732214  0.03804691  0.02730825]\n", " [-0.0215295   0.06858592 -0.01934098]\n", " [ 0.01783898 -0.05790293  0.00878389]\n", " [ 0.03658113 -0.02294942  0.03586165]\n", " [-0.01783979  0.05789769 -0.00877852]\n", " [ 0.02153202 -0.06860058  0.0193409 ]]\n", "\n", "CHGNet-predicted stress (GPa):\n", "[[-0.06165026  0.02226114  0.32380265]\n", " [ 0.02226108 -0.608527    0.0084345 ]\n", " [ 0.3238033   0.00843562 -0.3177392 ]]\n", "\n", "CHGNet-predicted magmom (mu_B):\n", "[0.00644356 0.00644353 0.00510713 0.00510694 0.00229147 0.00229168\n", " 0.00403076 0.01041824 0.00140774 0.0012143  0.00403079 0.00140786\n", " 0.00121418 0.00166646 0.01248461 0.01041847 0.01248473 0.00166664]\n", "\n", "CHGNet-predicted energy (eV/atom):\n", "-5.903668403625488\n", "\n", "CHGNet-predicted forces (eV/A):\n", "[[-0.16981083  0.1571615  -0.13510472]\n", " [ 0.16980076 -0.15716077  0.13509634]\n", " [ 0.14762485  0.0573535   0.1054275 ]\n", " [-0.147614   -0.05732393 -0.10541606]\n", " [-0.09833062  0.07583791 -0.1102469 ]\n", " [ 0.09832394 -0.07583869  0.1102519 ]\n", " [ 0.05236292  0.5594878   0.04700321]\n", " [-0.11240759 -0.623434   -0.14611062]\n", " [ 0.02106112 -0.18644066 -0.58544266]\n", " [-0.58375895 -0.17361563  0.15201768]\n", " [-0.05236208 -0.5594882  -0.04700756]\n", " [-0.02105945  0.18644279  0.5854467 ]\n", " [ 0.5837673   0.17361502 -0.15201864]\n", " [-0.07373887 -0.51295775 -0.10196664]\n", " [ 0.391984   -0.4724074   0.30078137]\n", " [ 0.1124038   0.62341577  0.14610653]\n", " [-0.3919825   0.4724057  -0.3007796 ]\n", " [ 0.07373625  0.5129468   0.10196259]]\n", "\n", "CHGNet-predicted stress (GPa):\n", "[[2.3233302  0.21628681 1.1625894 ]\n", " [0.21628574 3.4107068  0.3599006 ]\n", " [1.1625894  0.35990322 1.9076613 ]]\n", "\n", "CHGNet-predicted magmom (mu_B):\n", "[0.01032856 0.01032862 0.00762364 0.00762387 0.00497702 0.00497708\n", " 0.0004932  0.0058254  0.00177038 0.00202119 0.00049308 0.00177038\n", " 0.00202131 0.00296172 0.00817403 0.00582552 0.00817415 0.00296167]\n", "\n"]}], "source": ["from chgnet.model.model import CHGNet\n", "\n", "chgnet = CHGNet.load()\n", "prediction1 = chgnet.predict_structure(final_structure)\n", "\n", "for key, unit in [\n", "    (\"energy\", \"eV/atom\"),\n", "    (\"forces\", \"eV/A\"),\n", "    (\"stress\", \"GPa\"),\n", "    (\"magmom\", \"mu_B\"),\n", "]:\n", "    print(f\"CHGNet-predicted {key} ({unit}):\\n{prediction1[key[0]]}\\n\")\n", "\n", "prediction2 = chgnet.predict_structure(deformed_structure)\n", "\n", "for key, unit in [\n", "    (\"energy\", \"eV/atom\"),\n", "    (\"forces\", \"eV/A\"),\n", "    (\"stress\", \"GPa\"),\n", "    (\"magmom\", \"mu_B\"),\n", "]:\n", "    print(f\"CHGNet-predicted {key} ({unit}):\\n{prediction2[key[0]]}\\n\")"]}, {"cell_type": "markdown", "id": "7bc29a10-b777-4d21-b4a9-3444e130dd06", "metadata": {}, "source": ["# 원자가 일부 지워진 구조 여러 개 생성 및 에너지 평가"]}, {"cell_type": "code", "execution_count": 8, "id": "3ab11cea-cd04-4518-ae98-df55a8c41a0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Structure 1: Energy ranking = -3232.5333 (relative)\n", "Structure 2: Energy ranking = -3232.0807 (relative)\n", "Structure 3: Energy ranking = -3232.0807 (relative)\n", "Structure 4: Energy ranking = -3232.0807 (relative)\n", "Structure 5: Energy ranking = -3232.0807 (relative)\n", "Structure 6: Energy ranking = -3231.9802 (relative)\n", "Structure 7: Energy ranking = -3231.9802 (relative)\n", "Structure 8: Energy ranking = -3231.9734 (relative)\n", "Structure 9: Energy ranking = -3231.9734 (relative)\n", "Structure 10: Energy ranking = -3231.8940 (relative)\n"]}], "source": ["from pymatgen.transformations.standard_transformations import AutoOxiStateDecorationTransformation\n", "from pymatgen.transformations.standard_transformations import PartialRemoveSitesTransformation\n", "\n", "# 1. 2×2×2 supercell 생성\n", "supercell = final_structure.copy()\n", "supercell.make_supercell([2, 2, 2])  # 8배 확장\n", "\n", "# 2. 구조에 산화수 부여 (in-place 대체가 아닌 새 구조 반환)\n", "structure_with_oxi = AutoOxiStateDecorationTransformation().apply_transformation(supercell)\n", "\n", "# 3. Li site 인덱스 찾기\n", "li_indices = [i for i, site in enumerate(structure_with_oxi) if site.species_string == \"Li+\"]\n", "\n", "# 4. PartialRemoveSitesTransformation 정의\n", "# Li 중 절반(50%) 제거, electrostatic energy 기준 상위 10개만 유지\n", "t = PartialRemoveSitesTransformation(\n", "    [li_indices],\n", "    [0.5],\n", "    algo=0\n", ")\n", "\n", "# 5. 변환 적용 → 결손 구조 리스트 생성\n", "defect_structures = t.apply_transformation(structure_with_oxi, return_ranked_list=10)\n", "\n", "# 6. 결과 출력\n", "for i, entry in enumerate(defect_structures):\n", "    print(f\"Structure {i+1}: Energy ranking = {entry['energy']:.4f} (relative)\")"]}, {"cell_type": "code", "execution_count": null, "id": "14a8c027-d4cf-4ea9-8c41-104c6346454a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHGNet v0.3.0 initialized with 412,525 parameters\n", "CHGNet will run on mps\n"]}], "source": ["# 각 구조에 대한 MLP relaxation 에너지\n", "from chgnet.model import StructOptimizer\n", "relaxer = StructOptimizer()\n", "relaxed_structures = []\n", "for i, entry in enumerate(defect_structures):\n", "    result = relaxer.relax(entry['structure'], verbose=False)\n", "    relaxed_energy = result['trajectory'].energies[-1]\n", "    relaxed_structures.append(result['final_structure'])\n", "    print(f\"Structure {i+1}: MLP relaxed energy = {relaxed_energy:.4f}\")"]}, {"cell_type": "code", "execution_count": 11, "id": "35c6ce62-2112-4f32-a079-91d95f7f296b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"# generated using pymatgen\\ndata_Li(CoO2)2\\n_symmetry_space_group_name_H-M   'P 1'\\n_cell_length_a   11.60793431\\n_cell_length_b   11.68228352\\n_cell_length_c   11.63956990\\n_cell_angle_alpha   120.49852847\\n_cell_angle_beta   91.01316624\\n_cell_angle_gamma   59.41341771\\n_symmetry_Int_Tables_number   1\\n_chemical_formula_structural   Li(CoO2)2\\n_chemical_formula_sum   'Li16 Co32 O64'\\n_cell_volume   1107.50947610\\n_cell_formula_units_Z   16\\nloop_\\n _symmetry_equiv_pos_site_id\\n _symmetry_equiv_pos_as_xyz\\n  1  'x, y, z'\\nloop_\\n _atom_type_symbol\\n _atom_type_oxidation_number\\n  Li+  1.0\\n  Co3+  3.0\\n  O2-  -2.0\\nloop_\\n _atom_site_type_symbol\\n _atom_site_label\\n _atom_site_symmetry_multiplicity\\n _atom_site_fract_x\\n _atom_site_fract_y\\n _atom_site_fract_z\\n _atom_site_occupancy\\n  Li+  Li0  1  0.25000947  0.00000517  0.99999581  1\\n  Li+  Li1  1  0.25000960  0.50000505  0.49999565  1\\n  Li+  Li2  1  0.75000955  0.00000505  0.99999577  1\\n  Li+  Li3  1  0.75000955  0.50000534  0.49999577  1\\n  Li+  Li4  1  0.25001110  0.24998994  0.74997998  1\\n  Li+  Li5  1  0.25001098  0.74999008  0.24998003  1\\n  Li+  Li6  1  0.75001113  0.24998987  0.74997990  1\\n  Li+  Li7  1  0.75001117  0.74998993  0.24998004  1\\n  Li+  Li8  1  0.25030441  0.24481781  0.49479222  1\\n  Li+  Li9  1  0.24970350  0.25518578  1.00518988  1\\n  Li+  Li10  1  0.24970348  0.75518563  0.50518981  1\\n  Li+  Li11  1  0.25030437  0.74481789  0.99479228  1\\n  Li+  Li12  1  0.75030441  0.24481776  0.49479223  1\\n  Li+  Li13  1  0.74970359  0.25518567  1.00518986  1\\n  Li+  Li14  1  0.74970352  0.75518567  0.50518983  1\\n  Li+  Li15  1  0.75030445  0.74481785  0.99479224  1\\n  Co3+  Co16  1  0.00135691  0.24712621  0.50065347  1\\n  Co3+  Co17  1  -0.00134652  0.25287101  0.99933360  1\\n  Co3+  Co18  1  -0.00134655  0.75287107  0.49933364  1\\n  Co3+  Co19  1  0.00135696  0.74712611  1.00065328  1\\n  Co3+  Co20  1  0.50135695  0.24712621  0.50065348  1\\n  Co3+  Co21  1  0.49865354  0.25287100  0.99933360  1\\n  Co3+  Co22  1  0.49865345  0.75287103  0.49933357  1\\n  Co3+  Co23  1  0.50135701  0.74712607  1.00065335  1\\n  Co3+  Co24  1  0.00000529  0.24999837  0.24999311  1\\n  Co3+  Co25  1  0.00000545  0.24999871  0.74999344  1\\n  Co3+  Co26  1  0.00000548  0.74999873  0.24999349  1\\n  Co3+  Co27  1  0.00000532  0.74999840  0.74999308  1\\n  Co3+  Co28  1  0.50000533  0.24999835  0.24999298  1\\n  Co3+  Co29  1  0.50000551  0.24999869  0.74999346  1\\n  Co3+  Co30  1  0.50000557  0.74999868  0.24999353  1\\n  Co3+  Co31  1  0.50000533  0.74999842  0.74999305  1\\n  Co3+  Co32  1  0.25008208  0.49923809  0.24926570  1\\n  Co3+  Co33  1  0.24992840  0.50075939  0.75072118  1\\n  Co3+  Co34  1  0.24992846  1.00075932  0.25072121  1\\n  Co3+  Co35  1  0.25008207  0.99923799  0.74926565  1\\n  Co3+  Co36  1  0.75008210  0.49923799  0.24926562  1\\n  Co3+  Co37  1  0.74992851  0.50075935  0.75072120  1\\n  Co3+  Co38  1  0.74992843  1.00075931  0.25072124  1\\n  Co3+  Co39  1  0.75008204  0.99923806  0.74926573  1\\n  Co3+  Co40  1  0.00156609  0.49776294  0.25102816  1\\n  Co3+  Co41  1  -0.00155546  0.50223424  0.74895856  1\\n  Co3+  Co42  1  -0.00155543  1.00223418  0.24895846  1\\n  Co3+  Co43  1  0.00156610  0.99776298  0.75102809  1\\n  Co3+  Co44  1  0.50156603  0.49776300  0.25102809  1\\n  Co3+  Co45  1  0.49844458  0.50223422  0.74895860  1\\n  Co3+  Co46  1  0.49844458  1.00223424  0.24895855  1\\n  Co3+  Co47  1  0.50156609  0.99776291  0.75102817  1\\n  O2-  O48  1  0.38187306  0.49156467  0.14080329  1\\n  O2-  O49  1  0.38145025  0.47684236  0.62605797  1\\n  O2-  O50  1  0.38145045  0.97684219  0.12605783  1\\n  O2-  O51  1  0.38187321  0.99156462  0.64080330  1\\n  O2-  O52  1  0.88187320  0.49156464  0.14080332  1\\n  O2-  O53  1  0.88145030  0.47684226  0.62605791  1\\n  O2-  O54  1  0.88145026  0.97684222  0.12605786  1\\n  O2-  O55  1  0.88187326  0.99156457  0.64080329  1\\n  O2-  O56  1  0.37787905  0.27197860  0.12387194  1\\n  O2-  O57  1  0.37530887  0.26978197  0.63207581  1\\n  O2-  O58  1  0.37530893  0.76978204  0.13207593  1\\n  O2-  O59  1  0.37787898  0.77197863  0.62387192  1\\n  O2-  O60  1  0.87787897  0.27197870  0.12387207  1\\n  O2-  O61  1  0.87530886  0.26978194  0.63207578  1\\n  O2-  O62  1  0.87530890  0.76978189  0.13207569  1\\n  O2-  O63  1  0.87787898  0.77197864  0.62387203  1\\n  O2-  O64  1  0.10663816  0.26208300  0.12211257  1\\n  O2-  O65  1  0.10640321  0.27228388  0.63252775  1\\n  O2-  O66  1  0.10640325  0.77228383  0.13252762  1\\n  O2-  O67  1  0.10663816  0.76208300  0.62211257  1\\n  O2-  O68  1  0.60663812  0.26208292  0.12211255  1\\n  O2-  O69  1  0.60640328  0.27228379  0.63252759  1\\n  O2-  O70  1  0.60640323  0.77228389  0.13252767  1\\n  O2-  O71  1  0.60663811  0.76208291  0.62211249  1\\n  O2-  O72  1  0.37605248  0.49180011  0.35507811  1\\n  O2-  O73  1  0.37887898  0.49755505  0.85024291  1\\n  O2-  O74  1  0.37887904  0.99755504  0.35024291  1\\n  O2-  O75  1  0.37605252  0.99179991  0.85507809  1\\n  O2-  O76  1  0.87605253  0.49180010  0.35507812  1\\n  O2-  O77  1  0.87887897  0.49755497  0.85024289  1\\n  O2-  O78  1  0.87887904  0.99755495  0.35024288  1\\n  O2-  O79  1  0.87605253  0.99180008  0.85507809  1\\n  O2-  O80  1  0.12395817  0.00819675  0.14490834  1\\n  O2-  O81  1  0.12113167  0.00244270  0.64974377  1\\n  O2-  O82  1  0.12113169  0.50244274  0.14974371  1\\n  O2-  O83  1  0.12395805  0.50819684  0.64490836  1\\n  O2-  O84  1  0.62395806  0.00819684  0.14490829  1\\n  O2-  O85  1  0.62113150  0.00244271  0.64974380  1\\n  O2-  O86  1  0.62113160  0.50244258  0.14974372  1\\n  O2-  O87  1  0.62395810  0.50819695  0.64490837  1\\n  O2-  O88  1  0.39337244  0.23791388  0.37787347  1\\n  O2-  O89  1  0.39360748  0.22771331  0.86745844  1\\n  O2-  O90  1  0.39360743  0.72771329  0.36745849  1\\n  O2-  O91  1  0.39337249  0.73791386  0.87787358  1\\n  O2-  O92  1  0.89337241  0.23791389  0.37787360  1\\n  O2-  O93  1  0.89360751  0.22771328  0.86745842  1\\n  O2-  O94  1  0.89360736  0.72771328  0.36745846  1\\n  O2-  O95  1  0.89337236  0.73791395  0.87787361  1\\n  O2-  O96  1  0.12213120  0.22801884  0.37611391  1\\n  O2-  O97  1  0.12470209  0.23021498  0.86791130  1\\n  O2-  O98  1  0.12470205  0.73021499  0.36791132  1\\n  O2-  O99  1  0.12213120  0.72801888  0.87611390  1\\n  O2-  O100  1  0.62213132  0.22801873  0.37611379  1\\n  O2-  O101  1  0.62470213  0.23021488  0.86791114  1\\n  O2-  O102  1  0.62470205  0.73021495  0.36791110  1\\n  O2-  O103  1  0.62213120  0.72801876  0.87611383  1\\n  O2-  O104  1  0.11813797  0.00843268  0.35918325  1\\n  O2-  O105  1  0.11856140  0.02315395  0.87392805  1\\n  O2-  O106  1  0.11856126  0.52315408  0.37392823  1\\n  O2-  O107  1  0.11813801  0.50843271  0.85918324  1\\n  O2-  O108  1  0.61813788  0.00843285  0.35918334  1\\n  O2-  O109  1  0.61856126  0.02315410  0.87392819  1\\n  O2-  O110  1  0.61856120  0.52315421  0.37392831  1\\n  O2-  O111  1  0.61813793  0.50843284  0.85918342  1\\n\""]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["relaxed_structures[6].to('./relaxed_partial.cif')"]}, {"cell_type": "markdown", "id": "e76c1c95-58ca-4056-939b-177900b12b7d", "metadata": {}, "source": ["# Space Group 찾기"]}, {"cell_type": "code", "execution_count": 12, "id": "317cb283-583b-49fa-9efe-aff1af472a0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Space group symbol: P-1\n", "Space group number: 2\n", "International notation: P-1 (2) spacegroup\n"]}], "source": ["from pymatgen.symmetry.analyzer import SpacegroupAnalyzer\n", "\n", "# 공간군 분석기 생성\n", "sga = SpacegroupAnalyzer(relaxed_structures[6], symprec=1e-3)\n", "\n", "# 공간군 이름 및 번호 출력\n", "print(\"Space group symbol:\", sga.get_space_group_symbol())\n", "print(\"Space group number:\", sga.get_space_group_number())\n", "print(\"International notation:\", sga.get_space_group_operations())"]}, {"cell_type": "markdown", "id": "407fa5a7-9616-41af-9683-c255675c0a48", "metadata": {}, "source": ["# 구조 유사성 검사"]}, {"cell_type": "code", "execution_count": 13, "id": "b5eb2e7a-a67f-4ea0-90cd-1ddbc137e2d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 구조 유사성: 다름\n"]}], "source": ["from pymatgen.analysis.structure_matcher import StructureMatcher\n", "\n", "# 구조 비교기 생성\n", "matcher = StructureMatcher()\n", "\n", "# 두 구조가 유사한지 판단\n", "is_match = matcher.fit(relaxed_structures[4], relaxed_structures[6])\n", "\n", "print(\"🔍 구조 유사성:\", \"유사함\" if is_match else \"다름\")"]}, {"cell_type": "markdown", "id": "1cd1cf2a-930d-460e-9ad3-96eddd514fba", "metadata": {}, "source": ["# 여러 원자가 partial occupancy를 이루는 구조 여러 개 생성 및 에너지 평가"]}, {"cell_type": "code", "execution_count": 29, "id": "ade8fe84-6bea-4c21-9847-5c118c60004f", "metadata": {}, "outputs": [], "source": ["from pymatgen.core import Element, Composition\n", "\n", "partial_structure = supercell.copy()\n", "\n", "# 치환할 부분 점유 종 정의\n", "co_mn_mix = {Element(\"Co\"): 0.5, El<PERSON>(\"Mn\"): 0.5}\n", "\n", "# Co 원자를 부분 점유 혼합으로 치환\n", "partial_structure.replace_species({Element(\"Co\"): co_mn_mix})\n", "\n", "# 산화수 적용\n", "partial_structure_with_oxi = AutoOxiStateDecorationTransformation().apply_transformation(partial_structure)"]}, {"cell_type": "code", "execution_count": 35, "id": "7f007c7b-55b1-4417-91fd-b5201198bbe4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Structure 1: Energy ranking = -3510.1922 (relative)\n", "Structure 2: Energy ranking = -3509.8836 (relative)\n", "Structure 3: Energy ranking = -3509.8159 (relative)\n", "Structure 4: Energy ranking = -3509.8159 (relative)\n", "Structure 5: Energy ranking = -3509.8103 (relative)\n", "Structure 6: Energy ranking = -3509.8103 (relative)\n", "Structure 7: Energy ranking = -3509.8103 (relative)\n", "Structure 8: Energy ranking = -3509.8103 (relative)\n", "Structure 9: Energy ranking = -3509.8079 (relative)\n", "Structure 10: Energy ranking = -3509.8079 (relative)\n"]}], "source": ["from pymatgen.transformations.advanced_transformations import OrderDisorderedStructureTransformation\n", "ordered_structures = OrderDisorderedStructureTransformation().apply_transformation(partial_structure_with_oxi, return_ranked_list=10)\n", "# 결과 출력\n", "for i, entry in enumerate(ordered_structures):\n", "    print(f\"Structure {i+1}: Energy ranking = {entry['energy']:.4f} (relative)\")"]}, {"cell_type": "code", "execution_count": 40, "id": "687c74bd-e636-4e7d-8650-57220c7a6f46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Structure 1: MLP relaxed energy = -879.7989\n", "Structure 2: MLP relaxed energy = -879.7584\n", "Structure 3: MLP relaxed energy = -880.2217\n", "Structure 4: MLP relaxed energy = -880.2217\n", "Structure 5: MLP relaxed energy = -880.2516\n", "Structure 6: MLP relaxed energy = -880.2516\n", "Structure 7: MLP relaxed energy = -880.2516\n", "Structure 8: MLP relaxed energy = -880.2516\n", "Structure 9: MLP relaxed energy = -880.4033\n", "Structure 10: MLP relaxed energy = -880.4033\n"]}], "source": ["# 각 구조에 대한 MLP relaxation 에너지\n", "\n", "relaxed_ordered_structures = []\n", "for i, entry in enumerate(ordered_structures):\n", "    result2 = relaxer.relax(entry['structure'], verbose=False)\n", "    relaxed_energy2 = result2['trajectory'].energies[-1]\n", "    relaxed_ordered_structures.append(result2['final_structure'])\n", "    print(f\"Structure {i+1}: MLP relaxed energy = {relaxed_energy2:.4f}\")"]}, {"cell_type": "code", "execution_count": 41, "id": "31f6c8dc-d300-42be-9884-1862551a7db8", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"# generated using pymatgen\\ndata_Li2MnCoO4\\n_symmetry_space_group_name_H-M   'P 1'\\n_cell_length_a   11.78756511\\n_cell_length_b   11.83394742\\n_cell_length_c   11.79183653\\n_cell_angle_alpha   120.42472495\\n_cell_angle_beta   91.09715176\\n_cell_angle_gamma   59.29370792\\n_symmetry_Int_Tables_number   1\\n_chemical_formula_structural   Li2MnCoO4\\n_chemical_formula_sum   'Li32 Mn16 Co16 O64'\\n_cell_volume   1154.16311934\\n_cell_formula_units_Z   16\\nloop_\\n _symmetry_equiv_pos_site_id\\n _symmetry_equiv_pos_as_xyz\\n  1  'x, y, z'\\nloop_\\n _atom_type_symbol\\n _atom_type_oxidation_number\\n  Li+  1.0\\n  Mn3+  3.0\\n  Co2+  2.0\\n  Co4+  4.0\\n  O2-  -2.0\\nloop_\\n _atom_site_type_symbol\\n _atom_site_label\\n _atom_site_symmetry_multiplicity\\n _atom_site_fract_x\\n _atom_site_fract_y\\n _atom_site_fract_z\\n _atom_site_occupancy\\n  Li+  Li0  1  0.25062596  -0.00329213  0.50079650  1\\n  Li+  Li1  1  0.25062619  -0.00329130  1.00079692  1\\n  Li+  Li2  1  0.25062616  0.49670787  0.50079623  1\\n  Li+  Li3  1  0.25062614  0.49670777  1.00079587  1\\n  Li+  Li4  1  0.74941244  0.00330676  0.49921305  1\\n  Li+  Li5  1  0.74941258  0.00330766  0.99921372  1\\n  Li+  Li6  1  0.74941259  0.50330696  0.49921313  1\\n  Li+  Li7  1  0.74941227  0.50330726  0.99921364  1\\n  Li+  Li8  1  0.24916813  0.24638712  0.25028415  1\\n  Li+  Li9  1  0.24916768  0.24638764  0.75028474  1\\n  Li+  Li10  1  0.24916824  0.74638676  0.25028422  1\\n  Li+  Li11  1  0.24916828  0.74638688  0.75028356  1\\n  Li+  Li12  1  0.75086849  0.25356620  0.24965143  1\\n  Li+  Li13  1  0.75086886  0.25356604  0.74965123  1\\n  Li+  Li14  1  0.75086832  0.75356673  0.24965197  1\\n  Li+  Li15  1  0.75086877  0.75356602  0.74965108  1\\n  Li+  Li16  1  0.00017750  0.49993077  0.49989484  1\\n  Li+  Li17  1  0.00017728  0.49993072  0.99989459  1\\n  Li+  Li18  1  0.00017880  0.99992946  0.49989399  1\\n  Li+  Li19  1  0.00018032  0.99992967  0.99989291  1\\n  Li+  Li20  1  0.50018855  0.49992062  0.49989430  1\\n  Li+  Li21  1  0.50018783  0.49992286  0.99989450  1\\n  Li+  Li22  1  0.50018336  0.99992497  0.49989663  1\\n  Li+  Li23  1  0.50019224  0.99992041  0.99989231  1\\n  Li+  Li24  1  0.24481026  0.25085148  0.50204819  1\\n  Li+  Li25  1  0.24481071  0.25085062  1.00204855  1\\n  Li+  Li26  1  0.24481090  0.75085079  0.50204834  1\\n  Li+  Li27  1  0.24481070  0.75085081  1.00204832  1\\n  Li+  Li28  1  0.75519696  0.24913573  0.49795612  1\\n  Li+  Li29  1  0.75519623  0.24913552  0.99795626  1\\n  Li+  Li30  1  0.75519648  0.74913554  0.49795592  1\\n  Li+  Li31  1  0.75519665  0.74913523  0.99795606  1\\n  Mn3+  Mn32  1  0.49998829  0.25000665  0.25000171  1\\n  Mn3+  Mn33  1  0.49998855  0.25000645  0.75000191  1\\n  Mn3+  Mn34  1  0.49998882  0.75000647  0.25000141  1\\n  Mn3+  Mn35  1  0.49998821  0.75000631  0.75000158  1\\n  Mn3+  Mn36  1  0.25012014  0.49942454  0.24849899  1\\n  Mn3+  Mn37  1  0.25012075  0.49942366  0.74849878  1\\n  Mn3+  Mn38  1  0.25012033  0.99942419  0.24849925  1\\n  Mn3+  Mn39  1  0.25012033  0.99942348  0.74849880  1\\n  Mn3+  Mn40  1  0.74986890  0.50058039  0.25149795  1\\n  Mn3+  Mn41  1  0.74986837  0.50058036  0.75149818  1\\n  Mn3+  Mn42  1  0.74986889  1.00058048  0.25149799  1\\n  Mn3+  Mn43  1  0.74986924  1.00057980  0.75149765  1\\n  Mn3+  Mn44  1  -0.00000327  0.50000402  0.24998864  1\\n  Mn3+  Mn45  1  -0.00000340  0.50000326  0.74998788  1\\n  Mn3+  Mn46  1  -0.00000428  1.00000427  0.24998839  1\\n  Mn3+  Mn47  1  -0.00000368  1.00000336  0.74998825  1\\n  Co2+  Co48  1  -0.00000797  0.25000761  0.49999974  1\\n  Co2+  Co49  1  -0.00000806  0.25000797  0.99999971  1\\n  Co2+  Co50  1  -0.00000760  0.75000745  0.49999922  1\\n  Co2+  Co51  1  -0.00000798  0.75000773  0.99999937  1\\n  Co2+  Co52  1  0.49999063  0.25001643  0.50000395  1\\n  Co2+  Co53  1  0.49999041  0.25001631  1.00000325  1\\n  Co2+  Co54  1  0.49999072  0.75001528  0.50000274  1\\n  Co2+  Co55  1  0.49999017  0.75001596  1.00000340  1\\n  Co4+  Co56  1  -0.00000739  0.25000716  0.25000321  1\\n  Co4+  Co57  1  -0.00000775  0.25000718  0.75000273  1\\n  Co4+  Co58  1  -0.00000762  0.75000691  0.25000310  1\\n  Co4+  Co59  1  -0.00000792  0.75000753  0.75000365  1\\n  Co4+  Co60  1  0.49999097  0.50000277  0.24999611  1\\n  Co4+  Co61  1  0.49999132  0.50000195  0.74999539  1\\n  Co4+  Co62  1  0.49999076  1.00000250  0.24999584  1\\n  Co4+  Co63  1  0.49999096  1.00000245  0.74999525  1\\n  O2-  O64  1  0.36825844  0.49735746  0.13507360  1\\n  O2-  O65  1  0.36825856  0.49735771  0.63507428  1\\n  O2-  O66  1  0.36825830  0.99735820  0.13507482  1\\n  O2-  O67  1  0.36825845  0.99735814  0.63507395  1\\n  O2-  O68  1  0.87034190  0.50142849  0.13834061  1\\n  O2-  O69  1  0.87034129  0.50142931  0.63834067  1\\n  O2-  O70  1  0.87034227  1.00142871  0.13834082  1\\n  O2-  O71  1  0.87034156  1.00142904  0.63834084  1\\n  O2-  O72  1  0.37691689  0.25930470  0.13745777  1\\n  O2-  O73  1  0.37691703  0.25930433  0.63745823  1\\n  O2-  O74  1  0.37691668  0.75930473  0.13745849  1\\n  O2-  O75  1  0.37691666  0.75930455  0.63745820  1\\n  O2-  O76  1  0.86287305  0.27061719  0.13335088  1\\n  O2-  O77  1  0.86287282  0.27061776  0.63335132  1\\n  O2-  O78  1  0.86287288  0.77061723  0.13335101  1\\n  O2-  O79  1  0.86287255  0.77061768  0.63335112  1\\n  O2-  O80  1  0.11316983  0.26632872  0.13340987  1\\n  O2-  O81  1  0.11317098  0.26632780  0.63340819  1\\n  O2-  O82  1  0.11317039  0.76632850  0.13340897  1\\n  O2-  O83  1  0.11317054  0.76632857  0.63340867  1\\n  O2-  O84  1  0.61093629  0.25522521  0.13598758  1\\n  O2-  O85  1  0.61093661  0.25522524  0.63598774  1\\n  O2-  O86  1  0.61093643  0.75522577  0.13598856  1\\n  O2-  O87  1  0.61093673  0.75522499  0.63598782  1\\n  O2-  O88  1  0.37509605  0.48467556  0.36422464  1\\n  O2-  O89  1  0.37509658  0.48467528  0.86422469  1\\n  O2-  O90  1  0.37509654  0.98467535  0.36422457  1\\n  O2-  O91  1  0.37509620  0.98467556  0.86422470  1\\n  O2-  O92  1  0.86179399  0.50387820  0.37120763  1\\n  O2-  O93  1  0.86179390  0.50387862  0.87120815  1\\n  O2-  O94  1  0.86179398  1.00387884  0.37120822  1\\n  O2-  O95  1  0.86179371  1.00387860  0.87120797  1\\n  O2-  O96  1  0.13817967  -0.00387113  0.12879336  1\\n  O2-  O97  1  0.13817963  -0.00387088  0.62879322  1\\n  O2-  O98  1  0.13817932  0.49612872  0.12879357  1\\n  O2-  O99  1  0.13817958  0.49612908  0.62879350  1\\n  O2-  O100  1  0.62487591  0.01534075  0.13577166  1\\n  O2-  O101  1  0.62487628  0.01534052  0.63577182  1\\n  O2-  O102  1  0.62487638  0.51534055  0.13577153  1\\n  O2-  O103  1  0.62487604  0.51534096  0.63577179  1\\n  O2-  O104  1  0.38904849  0.24478490  0.36400964  1\\n  O2-  O105  1  0.38904838  0.24478502  0.86400962  1\\n  O2-  O106  1  0.38904879  0.74478485  0.36400980  1\\n  O2-  O107  1  0.38904823  0.74478541  0.86401025  1\\n  O2-  O108  1  0.88681664  0.23367956  0.36658925  1\\n  O2-  O109  1  0.88681714  0.23367988  0.86658877  1\\n  O2-  O110  1  0.88681728  0.73367917  0.36658838  1\\n  O2-  O111  1  0.88681697  0.73367942  0.86658901  1\\n  O2-  O112  1  0.13710262  0.22939884  0.36665373  1\\n  O2-  O113  1  0.13710260  0.22939913  0.86665444  1\\n  O2-  O114  1  0.13710278  0.72939884  0.36665407  1\\n  O2-  O115  1  0.13710308  0.72939864  0.86665432  1\\n  O2-  O116  1  0.62306023  0.24071077  0.36254066  1\\n  O2-  O117  1  0.62306060  0.24071032  0.86254117  1\\n  O2-  O118  1  0.62306085  0.74071013  0.36254070  1\\n  O2-  O119  1  0.62306041  0.74071085  0.86254087  1\\n  O2-  O120  1  0.12962277  -0.00141693  0.36167834  1\\n  O2-  O121  1  0.12962230  -0.00141606  0.86167889  1\\n  O2-  O122  1  0.12962269  0.49858374  0.36167858  1\\n  O2-  O123  1  0.12962286  0.49858351  0.86167859  1\\n  O2-  O124  1  0.63170916  0.00265362  0.36494056  1\\n  O2-  O125  1  0.63170825  0.00265498  0.86494175  1\\n  O2-  O126  1  0.63170964  0.50265380  0.36494093  1\\n  O2-  O127  1  0.63170884  0.50265458  0.86494128  1\\n\""]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["relaxed_ordered_structures[9].to('./relaxed_ordered_partial.cif')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}